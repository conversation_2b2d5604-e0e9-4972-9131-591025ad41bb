@model OrderFlowCore.Web.ViewModels.AssistantManagerViewModel
@{
    ViewData["Title"] = "مساعد المدير - إدارة الطلبات";
}

<style>
    .submit-button {
        border-style: none;
        border-color: inherit;
        border-width: medium;
        padding: 12px;
        background-color: #4CAF50;
        color: white;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        margin-top: 20px;
        margin-bottom: 15px;
        transition: background-color 0.3s ease;
    }
    .submit-button:hover {
        background-color: #45a049;
    }
    .reject-button {
        border-style: none;
        border-color: inherit;
        border-width: medium;
        padding: 12px;
        background-color: red;
        color: white;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        margin-top: 20px;
        margin-bottom: 15px;
        transition: background-color 0.3s ease;
    }
    .reject-button:hover {
        background-color: #dd0000;
    }
    .details-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        overflow: hidden;
        border: 2px solid #007bff;
    }
    .details-table th,
    .details-table td {
        padding: 16px;
        text-align: center;
        border: 1px solid #ddd;
    }
    .details-table th {
        background-color: #f2f2f2;
        color: #333;
        font-weight: bold;
        font-size: 16px;
        border-right: 1px solid #ddd;
    }
    .details-table td {
        font-size: 16px;
        color: #333;
        background-color: #ffffff;
    }
    .details-table tr:nth-child(even) td {
        background-color: #ffffff;
    }
    .details-table tr:hover td {
        background-color: #e9e9e9;
    }
    .details-table th:first-child,
    .details-table td:first-child {
        border-left: none;
    }
    .details-table th:last-child,
    .details-table td:last-child {
        border-right: none;
    }
    .details-table tr:first-child th:first-child {
        border-top-left-radius: 12px;
    }
    .details-table tr:first-child th:last-child {
        border-top-right-radius: 12px;
    }
    .details-table tr:last-child td:first-child {
        border-bottom-left-radius: 12px;
    }
    .details-table tr:last-child td:last-child {
        border-bottom-right-radius: 12px;
    }
    @@media (max-width: 768px) {
        .details-table th,
        .details-table td {
            font-size: 12px;
            padding: 8px;
        }
    }
    .full-table-container {
        border: 3px solid #007bff;
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        margin-top: 20px;
    }
    body {
        background-color: #f4f7fa;
    }
    .order-selection {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    .loading {
        display: none;
        text-align: center;
        padding: 20px;
    }
    .order-details {
        display: none;
    }
</style>

@Html.AntiForgeryToken()

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">مساعد المدير - إدارة الطلبات</h2>
            <!-- Order Selection Section -->
            <div class="order-selection">
                <div class="row align-items-start">
                    <div class="col-md-4">
                        <label for="orderId" class="form-label">رقم الطلب:</label>
                        <select id="orderId" class="form-select" style="font-size: 20px;" asp-for="SelectedOrderId" asp-items="Model.OrderNumbers">
                            <option value="">اختر الطلب</option>
                        </select>
                    </div>
                    <div class="col-md-8">
                        <button type="button" id="confirmOrderBtn" class="btn submit-button" style="display: none;">اعتماد</button>
                        <textarea id="returnReason" class="form-control auto-expand" placeholder="سبب الإعادة إلى مدير القسم" style="display: none;"></textarea>
                        <button type="button" id="returnToManagerBtn" class="btn submit-button" style="display: none; background-color: #007bff;">إعادة إلى مدير القسم</button>
                        <textarea id="rejectReason" class="form-control reason-textbox auto-expand" placeholder="سبب الإلغاء" style="display: none;"></textarea>
                        <button type="button" id="rejectOrderBtn" class="btn reject-button" style="display: none;">إلغاء الطلب</button>
                        <button type="button" id="downloadAttachmentsBtn" class="btn submit-button" style="display: none;">تحميل مرفقات الطلب</button>
                    </div>
                </div>
                <div id="messageContainer" class="mt-3"></div>
            </div>
            <!-- Loading -->
            <div id="loading" class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل تفاصيل الطلب...</p>
            </div>
            <!-- Order Details Section -->
            <div id="orderDetails" class="order-details">
                <div class="full-table-container">
                    <!-- Table 1: Basic Order Info -->
                    <table class="details-table">
                        <tr>
                            <th>رقم الطلب</th>
                            <td id="orderNumber"></td>
                            <th>تاريخ الطلب</th>
                            <td id="orderDate"></td>
                        </tr>
                        <tr>
                            <th>حالة الطلب</th>
                            <td colspan="3" id="orderStatus"></td>
                        </tr>
                    </table>
                    <!-- Table 2: Employee Details -->
                    <table class="details-table">
                        <tr>
                            <th>نوع الطلب</th>
                            <td colspan="3" id="orderType"></td>
                        </tr>
                        <tr>
                            <th>اسم الموظف</th>
                            <td id="employeeName"></td>
                            <th>القسم</th>
                            <td id="department"></td>
                        </tr>
                        <tr>
                            <th>الوظيفة</th>
                            <td id="jobTitle"></td>
                            <th></th>
                            <td></td>
                        </tr>
                        <tr>
                            <th>نوع التوظيف</th>
                            <td id="employmentType"></td>
                            <th>المؤهل</th>
                            <td id="qualification"></td>
                        </tr>
                        <tr>
                            <th>رقم الموظف</th>
                            <td id="employeeNumber"></td>
                            <th>السجل المدني</th>
                            <td id="civilRegistry"></td>
                        </tr>
                        <tr>
                            <th>الجنسية</th>
                            <td id="nationality"></td>
                            <th>رقم الجوال</th>
                            <td id="mobileNumber"></td>
                        </tr>
                        <tr id="notesRow" style="display: none;">
                            <td colspan="4">
                                <strong>تفاصيل مقدم الطلب:</strong><br />
                                <span id="notes"></span>
                            </td>
                        </tr>
                    </table>
                    <!-- Table 3: Approval Status -->
                    <table class="details-table">
                        <tr>
                            <th>تم التأكيد/الإلغاء من مدير القسم</th>
                            <th>تم التأكيد/الإلغاء من قبل مساعد المدير</th>
                            <th>تم التحويل/الإلغاء من قبل المنسق</th>
                        </tr>
                        <tr>
                            <td id="managerApproval"></td>
                            <td id="supervisorApproval"></td>
                            <td id="coordinatorApproval"></td>
                        </tr>
                        <tr id="cancellationRow" style="display: none;">
                            <th>سبب الإلغاء/الإعادة</th>
                            <th colspan="2">تفاصيل المنسق</th>
                        </tr>
                        <tr id="cancellationDetailsRow" style="display: none;">
                            <td id="cancellationReason"></td>
                            <td colspan="2" id="coordinatorDetails"></td>
                        </tr>
                    </table>
                    <!-- Table 4: Supervisors Permissions -->
                    <table class="details-table">
                        <tr>
                            <th>مشرف خدمات الموظفين</th>
                            <td id="medicalServicesPermission"></td>
                            <th>مشرف إدارة تخطيط الموارد البشرية</th>
                            <td id="hrPlanningPermission"></td>
                        </tr>
                        <tr>
                            <th>مشرف إدارة تقنية المعلومات</th>
                            <td id="itPermission"></td>
                            <th>مشرف مراقبة الدوام</th>
                            <td id="attendanceControlPermission"></td>
                        </tr>
                        <tr>
                            <th>مشرف السجلات الطبية</th>
                            <td id="medicalRecordsPermission"></td>
                            <th>مشرف إدارة الرواتب والاستحقاقات</th>
                            <td id="payrollPermission"></td>
                        </tr>
                        <tr>
                            <th>مشرف إدارة القانونية والالتزام</th>
                            <td id="legalCompliancePermission"></td>
                            <th>مشرف خدمات الموارد البشرية</th>
                            <td id="hrServicesPermission"></td>
                        </tr>
                        <tr>
                            <th>مشرف إدارة الإسكان</th>
                            <td id="housingPermission"></td>
                            <th>مشرف قسم الملفات</th>
                            <td id="filesSectionPermission"></td>
                        </tr>
                        <tr>
                            <th>مشرف العيادات الخارجية</th>
                            <td id="outpatientPermission"></td>
                            <th>مشرف التأمينات الاجتماعية</th>
                            <td id="socialInsurancePermission"></td>
                        </tr>
                        <tr>
                            <th>مشرف وحدة مراقبة المخزون</th>
                            <td id="inventoryControlPermission"></td>
                            <th>مشرف إدارة تنمية الإيرادات</th>
                            <td id="selfResourcesPermission"></td>
                        </tr>
                        <tr>
                            <th>مشرف إدارة الأمن و السلامة</th>
                            <td id="nursingPermission"></td>
                            <th>مشرف الطب الاتصالي</th>
                            <td id="employeeServicesPermission"></td>
                        </tr>
                    </table>
                    <!-- Table 5: HR Manager -->
                    <table class="details-table">
                        <tr>
                            <th>مدير الموارد البشرية</th>
                            <td colspan="3" id="hrManagerApproval"></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        var textAreas = document.querySelectorAll(".auto-expand");
        textAreas.forEach(function (textarea) {
            textarea.addEventListener("input", function () {
                this.style.height = "auto";
                this.style.height = (this.scrollHeight) + "px";
            });
        });
        var currentOrderId = null;
        document.getElementById('orderId').addEventListener('change', function() {
            var orderId = this.value;
            if (orderId && orderId !== '') {
                loadOrderDetails(orderId);
            } else {
                hideOrderDetails();
            }
        });
        document.getElementById('confirmOrderBtn').addEventListener('click', function() {
            if (currentOrderId) {
                confirmOrder(currentOrderId);
            }
        });
        document.getElementById('returnToManagerBtn').addEventListener('click', function() {
            if (currentOrderId) {
                var returnReason = document.getElementById('returnReason').value.trim();
                if (!returnReason) {
                    showMessage('يرجى إدخال سبب الإعادة إلى مدير القسم.', 'error');
                    return;
                }
                returnToManager(currentOrderId, returnReason);
            }
        });
        document.getElementById('rejectOrderBtn').addEventListener('click', function() {
            if (currentOrderId) {
                var rejectReason = document.getElementById('rejectReason').value.trim();
                if (!rejectReason) {
                    showMessage('يرجى إدخال سبب الإلغاء.', 'error');
                    return;
                }
                rejectOrder(currentOrderId, rejectReason);
            }
        });
        document.getElementById('downloadAttachmentsBtn').addEventListener('click', function() {
            if (currentOrderId) {
                window.location.href = `/AssistantManager/DownloadAttachments?orderId=${currentOrderId}`;
            }
        });
        function loadOrderDetails(orderId) {
            currentOrderId = orderId;
            showLoading();

            $.ajax({
                url: '/AssistantManager/GetOrderDetails',
                type: 'POST',
                data: { orderId: orderId },
                success: function (response) {
                    hideLoading();
                    if (response.success) {
                        populateOrderDetails(response.data);
                        showOrderDetails();
                    } else {
                        showMessage(response.message, 'error');
                    }
                },
                error: function () {
                    hideLoading();
                    showMessage('حدث خطأ أثناء تحميل تفاصيل الطلب', 'error');
                }
            });
        }
        function populateOrderDetails(data) {
            $('#orderNumber').text(data.orderNumber);
            $('#orderDate').text(data.orderDate);
            $('#orderStatus').text(data.orderStatus);
            $('#orderType').text(data.orderType);
            $('#employeeName').text(data.employeeName);
            $('#department').text(data.department);
            $('#jobTitle').text(data.jobTitle);
            $('#employmentType').text(data.employmentType);
            $('#qualification').text(data.qualification);
            $('#employeeNumber').text(data.employeeNumber);
            $('#civilRegistry').text(data.civilRegistry);
            $('#nationality').text(data.nationality);
            $('#mobileNumber').text(data.mobileNumber);

            if (data.notes) {
                $('#notes').text(data.notes);
                $('#notesRow').show();
            } else {
                $('#notesRow').hide();
            }

            $('#managerApproval').text(data.managerApproval || 'قيد الانتظار');
            $('#supervisorApproval').text(data.supervisorApproval || 'قيد الانتظار');
            $('#coordinatorApproval').text(data.coordinatorApproval || 'قيد الانتظار');

            if (data.cancellationReason) {
                $('#cancellationReason').text(data.cancellationReason);
                $('#cancellationRow, #cancellationDetailsRow').show();
            } else {
                $('#cancellationRow, #cancellationDetailsRow').hide();
            }

            if (data.coordinatorDetails) {
                $('#coordinatorDetails').text(data.coordinatorDetails);
            }

            // Supervisors permissions
            $('#medicalServicesPermission').text(data.medicalServicesPermission || 'قيد الانتظار');
            $('#hrPlanningPermission').text(data.hrPlanningPermission || 'قيد الانتظار');
            $('#itPermission').text(data.itPermission || 'قيد الانتظار');
            $('#attendanceControlPermission').text(data.attendanceControlPermission || 'قيد الانتظار');
            $('#medicalRecordsPermission').text(data.medicalRecordsPermission || 'قيد الانتظار');
            $('#payrollPermission').text(data.payrollPermission || 'قيد الانتظار');
            $('#legalCompliancePermission').text(data.legalCompliancePermission || 'قيد الانتظار');
            $('#hrServicesPermission').text(data.hrServicesPermission || 'قيد الانتظار');
            $('#housingPermission').text(data.housingPermission || 'قيد الانتظار');
            $('#filesSectionPermission').text(data.filesSectionPermission || 'قيد الانتظار');
            $('#outpatientPermission').text(data.outpatientPermission || 'قيد الانتظار');
            $('#socialInsurancePermission').text(data.socialInsurancePermission || 'قيد الانتظار');
            $('#inventoryControlPermission').text(data.inventoryControlPermission || 'قيد الانتظار');
            $('#selfResourcesPermission').text(data.selfResourcesPermission || 'قيد الانتظار');
            $('#nursingPermission').text(data.nursingPermission || 'قيد الانتظار');
            $('#employeeServicesPermission').text(data.employeeServicesPermission || 'قيد الانتظار');

            $('#hrManagerApproval').text(data.hrManagerApproval || 'قيد الانتظار');
        }
        function confirmOrder(orderId) {
            if (confirm('هل أنت متأكد من تأكيد هذا الطلب؟')) {
                submitForm('/AssistantManager/ConfirmOrder', { orderId: orderId });
            }
        }
        function returnToManager(orderId, returnReason) {
            if (confirm('هل أنت متأكد من إعادة هذا الطلب إلى مدير القسم؟')) {
                submitForm('/AssistantManager/ReturnToManager', { orderId: orderId, returnReason: returnReason });
            }
        }
        function rejectOrder(orderId, rejectReason) {
            if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                submitForm('/AssistantManager/RejectOrder', { orderId: orderId, rejectReason: rejectReason });
            }
        }
        function showLoading() {
            document.getElementById('loading').style.display = '';
            document.getElementById('orderDetails').style.display = 'none';
        }
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        function showOrderDetails() {
            $('#orderDetails').show();
            $('#returnReason').show();
            $('#confirmOrderBtn, #rejectOrderBtn, #downloadAttachmentsBtn').show();
            $('#rejectReason').show();
            $('#returnToManagerBtn').show();
        }
        function hideOrderDetails() {
            document.getElementById('orderDetails').style.display = 'none';
            document.getElementById('confirmOrderBtn').style.display = 'none';
            document.getElementById('returnReason').style.display = 'none';
            document.getElementById('returnToManagerBtn').style.display = 'none';
            document.getElementById('rejectReason').style.display = 'none';
            document.getElementById('rejectOrderBtn').style.display = 'none';
            document.getElementById('downloadAttachmentsBtn').style.display = 'none';
            currentOrderId = null;
        }
        function showMessage(message, type) {
            var alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
            document.getElementById('messageContainer').innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
        }
        // Show success/error messages from TempData
        @if (TempData["SuccessMessage"] != null)
        {
            <text>showMessage('@TempData["SuccessMessage"]', 'success');</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>showMessage('@TempData["ErrorMessage"]', 'error');</text>
        }
        function submitForm(url, data) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            form.style.display = 'none';
            // Add anti-forgery token
            var token = document.querySelector('input[name="__RequestVerificationToken"]');
            if (token) {
                var tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = '__RequestVerificationToken';
                tokenInput.value = token.value;
                form.appendChild(tokenInput);
            }
            // Add data fields
            for (var key in data) {
                var input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = data[key];
                form.appendChild(input);
            }
            document.body.appendChild(form);
            form.submit();
        }
    });
</script> 