using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Services
{
    public class DashboardService : IDashboardService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPerformanceMonitoringService _performanceService;
        private readonly ILogger<DashboardService> _logger;

        public DashboardService(
            IUnitOfWork unitOfWork,
            IPerformanceMonitoringService performanceService,
            ILogger<DashboardService> logger)
        {
            _unitOfWork = unitOfWork;
            _performanceService = performanceService;
            _logger = logger;
        }

        public async Task<ServiceResult<DashboardStatisticsDto>> GetDashboardStatisticsAsync()
        {
            try
            {
                _logger.LogInformation("Starting dashboard statistics collection");

                var statistics = new DashboardStatisticsDto();

                // Collect all statistics in parallel for better performance
                var orderStatsTask = GetOrderStatisticsAsync();
                var departmentStatsTask = GetDepartmentStatisticsAsync();
                var employeeStatsTask = GetEmployeeStatisticsAsync();
                var recentActivityTask = GetRecentActivityAsync();
                var performanceMetricsTask = GetPerformanceMetricsAsync();
                var workflowStatsTask = GetWorkflowStatisticsAsync();

                await Task.WhenAll(orderStatsTask, departmentStatsTask, employeeStatsTask,
                    recentActivityTask, performanceMetricsTask, workflowStatsTask);

                statistics.OrderStatistics = await orderStatsTask;
                statistics.DepartmentStatistics = await departmentStatsTask;
                statistics.EmployeeStatistics = await employeeStatsTask;
                statistics.RecentActivity = await recentActivityTask;
                statistics.PerformanceMetrics = await performanceMetricsTask;
                statistics.WorkflowStatistics = await workflowStatsTask;

                _logger.LogInformation("Dashboard statistics collection completed successfully");
                return ServiceResult<DashboardStatisticsDto>.Success(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting dashboard statistics");
                return ServiceResult<DashboardStatisticsDto>.Failure("حدث خطأ أثناء جمع إحصائيات لوحة التحكم");
            }
        }

        private async Task<OrderStatisticsDto> GetOrderStatisticsAsync()
        {
            var orders = await _unitOfWork.Orders.GetAllAsync();
            var orderTypes = await _unitOfWork.OrdersTypes.GetAllAsync();

            var now = DateTime.Now;
            var today = now.Date;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(now.Year, now.Month, 1);

            var totalOrders = orders.Count;
            var pendingStatuses = new[] { OrderStatus.DM, OrderStatus.A1, OrderStatus.A2, OrderStatus.A3, OrderStatus.A4, OrderStatus.B, OrderStatus.C, OrderStatus.D };
            var cancelledStatuses = new[] { OrderStatus.CancelledByDepartmentManager, OrderStatus.CancelledByAssistantManager, OrderStatus.CancelledBySupervisor, OrderStatus.CancelledByCoordinator, OrderStatus.CancelledByManager };

            return new OrderStatisticsDto
            {
                TotalOrders = totalOrders,
                PendingOrders = orders.Count(o => pendingStatuses.Contains(o.OrderStatus)),
                CompletedOrders = orders.Count(o => o.OrderStatus == OrderStatus.Accepted),
                CancelledOrders = orders.Count(o => cancelledStatuses.Contains(o.OrderStatus)),
                TodayOrders = orders.Count(o => o.CreatedAt.Date == today),
                ThisWeekOrders = orders.Count(o => o.CreatedAt.Date >= weekStart),
                ThisMonthOrders = orders.Count(o => o.CreatedAt.Date >= monthStart),
                OrdersByStatus = orders.GroupBy(o => o.OrderStatus)
                    .Select(g => new OrderStatusCountDto
                    {
                        Status = g.Key,
                        StatusDisplayName = g.Key.ToDisplayString(),
                        Count = g.Count(),
                        Percentage = totalOrders > 0 ? (double)g.Count() / totalOrders * 100 : 0
                    }).OrderByDescending(x => x.Count).ToList(),
                OrdersByType = orders.GroupBy(o => o.OrderType)
                    .Select(g => new OrderTypeCountDto
                    {
                        OrderType = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalOrders > 0 ? (double)g.Count() / totalOrders * 100 : 0
                    }).OrderByDescending(x => x.Count).ToList(),
                OrdersByDepartment = orders.GroupBy(o => o.Department)
                    .Select(g => new DepartmentOrderCountDto
                    {
                        DepartmentName = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalOrders > 0 ? (double)g.Count() / totalOrders * 100 : 0
                    }).OrderByDescending(x => x.Count).Take(10).ToList()
            };
        }

        private async Task<DepartmentStatisticsDto> GetDepartmentStatisticsAsync()
        {
            var departments = await _unitOfWork.Departments.GetAllAsync();
            var totalDepartments = departments.Count();

            return new DepartmentStatisticsDto
            {
                TotalDepartments = totalDepartments,
                AssignedDepartments = departments.Count(d => d.AssistantManagerId != AssistantManagerType.Unknown),
                UnassignedDepartments = departments.Count(d => d.AssistantManagerId == AssistantManagerType.Unknown),
                DepartmentsByManager = departments
                    .Where(d => d.AssistantManagerId != AssistantManagerType.Unknown)
                    .GroupBy(d => d.AssistantManagerId)
                    .Select(g => new ManagerDepartmentCountDto
                    {
                        ManagerType = g.Key,
                        ManagerName = g.Key.ToDisplayName(),
                        DepartmentCount = g.Count(),
                        Percentage = totalDepartments > 0 ? (double)g.Count() / totalDepartments * 100 : 0
                    }).OrderByDescending(x => x.DepartmentCount).ToList()
            };
        }

        private async Task<EmployeeStatisticsDto> GetEmployeeStatisticsAsync()
        {
            var employees = await _unitOfWork.Employees.GetAllAsync();
            var totalEmployees = employees.Count();

            return new EmployeeStatisticsDto
            {
                TotalEmployees = totalEmployees,
                EmployeesByNationality = employees.GroupBy(e => e.Nationality)
                    .Select(g => new NationalityCountDto
                    {
                        Nationality = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalEmployees > 0 ? (double)g.Count() / totalEmployees * 100 : 0
                    }).OrderByDescending(x => x.Count).Take(10).ToList(),
                EmployeesByType = employees.GroupBy(e => e.EmploymentType)
                    .Select(g => new EmploymentTypeCountDto
                    {
                        EmploymentType = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalEmployees > 0 ? (double)g.Count() / totalEmployees * 100 : 0
                    }).OrderByDescending(x => x.Count).ToList(),
                EmployeesByQualification = employees.GroupBy(e => e.Qualification)
                    .Select(g => new QualificationCountDto
                    {
                        Qualification = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalEmployees > 0 ? (double)g.Count() / totalEmployees * 100 : 0
                    }).OrderByDescending(x => x.Count).Take(10).ToList()
            };
        }

        private async Task<RecentActivityDto> GetRecentActivityAsync()
        {
            var orders = await _unitOfWork.Orders.GetAllAsync();
            var recentOrders = orders
                .OrderByDescending(o => o.CreatedAt)
                .Take(10)
                .Select(o => new RecentOrderDto
                {
                    Id = o.Id,
                    EmployeeName = o.EmployeeName ?? "غير محدد",
                    OrderType = o.OrderType ?? "غير محدد",
                    Department = o.Department ?? "غير محدد",
                    CreatedAt = o.CreatedAt,
                    Status = o.OrderStatus,
                    StatusDisplayName = o.OrderStatus.ToDisplayString()
                }).ToList();

            // Mock recent activities - in a real system, you'd have an audit log
            var recentActivities = new List<ActivityLogDto>
            {
                new() { Action = "إنشاء طلب", Description = "تم إنشاء طلب جديد", Timestamp = DateTime.Now.AddMinutes(-5), UserName = "أحمد محمد" },
                new() { Action = "موافقة", Description = "تمت الموافقة على طلب", Timestamp = DateTime.Now.AddMinutes(-15), UserName = "سارة أحمد" },
                new() { Action = "إعادة", Description = "تم إعادة طلب للمراجعة", Timestamp = DateTime.Now.AddMinutes(-30), UserName = "محمد علي" }
            };

            return new RecentActivityDto
            {
                RecentOrders = recentOrders,
                RecentActivities = recentActivities
            };
        }

        private async Task<PerformanceMetricsDto> GetPerformanceMetricsAsync()
        {
            var metrics = await _performanceService.GetPerformanceMetricsAsync();
            
            return new PerformanceMetricsDto
            {
                AverageProcessingTime = 0.0, // Would be calculated from actual performance data
                SlowQueries = 0,
                SystemUptime = 99.9,
                AdditionalMetrics = metrics
            };
        }

        private async Task<WorkflowStatisticsDto> GetWorkflowStatisticsAsync()
        {
            var orders = await _unitOfWork.Orders.GetAllAsync();

            var workflowStats = new WorkflowStatisticsDto
            {
                OrdersAtDirectManager = orders.Count(o => o.OrderStatus == OrderStatus.DM),
                OrdersAtAssistantManagers = orders.Count(o => o.OrderStatus == OrderStatus.A1 || o.OrderStatus == OrderStatus.A2 || o.OrderStatus == OrderStatus.A3 || o.OrderStatus == OrderStatus.A4),
                OrdersAtCoordinators = orders.Count(o => o.OrderStatus == OrderStatus.B),
                OrdersAtSupervisors = orders.Count(o => o.OrderStatus == OrderStatus.C),
                OrdersAtManagers = orders.Count(o => o.OrderStatus == OrderStatus.D),
                OrdersRequiringAction = orders.Count(o => o.OrderStatus == OrderStatus.ActionRequired || o.OrderStatus == OrderStatus.ActionRequiredBySupervisor)
            };

            workflowStats.OrdersByWorkflowStage = new List<WorkflowStageCountDto>
            {
                new() { StageName = "مدير القسم", Count = workflowStats.OrdersAtDirectManager, StageColor = "#007bff" },
                new() { StageName = "مساعد المدير", Count = workflowStats.OrdersAtAssistantManagers, StageColor = "#28a745" },
                new() { StageName = "المنسق", Count = workflowStats.OrdersAtCoordinators, StageColor = "#ffc107" },
                new() { StageName = "المشرف", Count = workflowStats.OrdersAtSupervisors, StageColor = "#17a2b8" },
                new() { StageName = "المدير", Count = workflowStats.OrdersAtManagers, StageColor = "#6f42c1" },
                new() { StageName = "يتطلب إجراء", Count = workflowStats.OrdersRequiringAction, StageColor = "#dc3545" }
            };

            var totalWorkflowOrders = workflowStats.OrdersByWorkflowStage.Sum(s => s.Count);
            foreach (var stage in workflowStats.OrdersByWorkflowStage)
            {
                stage.Percentage = totalWorkflowOrders > 0 ? (double)stage.Count / totalWorkflowOrders * 100 : 0;
            }

            return workflowStats;
        }
    }
}
