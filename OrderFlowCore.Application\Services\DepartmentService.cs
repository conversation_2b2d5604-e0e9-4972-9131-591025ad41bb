using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;

public class DepartmentService : IDepartmentService
{
    private readonly IUnitOfWork _unitOfWork;

    public DepartmentService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<ServiceResult<IEnumerable<DepartmentDto>>> GetAllDepartmentsAsync()
    {
        try
        {
            var departments = await _unitOfWork.Departments.GetAllAsync();
            return ServiceResult<IEnumerable<DepartmentDto>>.Success(departments);
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<DepartmentDto>>.Failure($"Error retrieving departments: {ex.Message}");
        }
    }

    public async Task<ServiceResult<DepartmentDto>> GetDepartmentByIdAsync(int id)
    {
        try
        {
            var department = await _unitOfWork.Departments.GetByIdAsync(id);
            if (department == null)
                return ServiceResult<DepartmentDto>.Failure("Department not found");
                
            return ServiceResult<DepartmentDto>.Success(department);
        }
        catch (Exception ex)
        {
            return ServiceResult<DepartmentDto>.Failure($"Error retrieving department: {ex.Message}");
        }
    }

    public async Task<ServiceResult> CreateDepartmentAsync(DepartmentDto dto)
    {
        try
        {
            var exists = await _unitOfWork.Departments.ExistsAsync(dto.Name);
            if (exists)
            {
                return ServiceResult.Failure("القسم موجود بالفعل");
            }

            var result = await _unitOfWork.Departments.CreateAsync(dto);
            await _unitOfWork.SaveChangesAsync();
            return ServiceResult.Success("تم إضافة القسم بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure("حدث خطأ أثناء إضافة القسم",
                new List<string> { ex.Message });
        }
    }

    public async Task<ServiceResult> UpdateDepartmentAsync(DepartmentDto dto)
    {
        try
        {
            var exists = await _unitOfWork.Departments.ExistsAsync(dto.Name, dto.Id);
            if (exists)
            {
                return ServiceResult.Failure("القسم موجود بالفعل");
            }

            var result = await _unitOfWork.Departments.UpdateAsync(dto);
            if (result == null)
            {
                return ServiceResult.Failure("القسم غير موجود");
            }

            await _unitOfWork.SaveChangesAsync();
            return ServiceResult.Success("تم تحديث القسم بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure("حدث خطأ أثناء تحديث القسم",
                new List<string> { ex.Message });
        }
    }

    public async Task<ServiceResult> DeleteDepartmentAsync(int id)
    {
        try
        {
            var result = await _unitOfWork.Departments.DeleteAsync(id);
            if (!result)
            {
                return ServiceResult.Failure("القسم غير موجود");
            }

            await _unitOfWork.SaveChangesAsync();
            return ServiceResult.Success("تم حذف القسم بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure("حدث خطأ أثناء حذف القسم",
                new List<string> { ex.Message });
        }
    }
}