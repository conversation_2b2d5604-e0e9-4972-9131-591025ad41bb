using System;
using System.Collections.Generic;

namespace OrderFlowCore.Web.ViewModels
{
    public class OrderCoordinatorViewModel
    {
        // ... existing properties ...

        // Restore section
        public List<RestorableOrderDropdownItem> RestorableOrders { get; set; } = new List<RestorableOrderDropdownItem>();
        public string SelectedRestorableOrderNumber { get; set; }
        public string RestoreNotes { get; set; }
        public RestoreDetailsViewModel RestoreDetails { get; set; } = new RestoreDetailsViewModel();
        public bool ShowRestoreDetails { get; set; }
        public string RestoreSuccessMessage { get; set; }
        public string RestoreErrorMessage { get; set; }

        // Supervisors (checkboxes)
        public List<SupervisorCheckboxViewModel> Supervisors { get; set; } = new List<SupervisorCheckboxViewModel>();
        public List<string> CheckedSupervisors { get; set; } = new List<string>();
    }

    public class RestorableOrderDropdownItem
    {
        public string Value { get; set; }
        public string Text { get; set; }
    }

    public class RestoreDetailsViewModel
    {
        public string CurrentStatus { get; set; }
        public string TransferDate { get; set; }
        public string AssignedSupervisors { get; set; }
    }

    // ... existing classes ...
    public class SupervisorCheckboxViewModel
    {
        public string Name { get; set; }
        public bool IsChecked { get; set; }
    }
} 