using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Interfaces.Data;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Infrastructure.Data;

public class DatabaseSeeder : IDatabaseSeeder
{
    private readonly ApplicationDbContext _context;
    private readonly IAuthService _authService;

    public DatabaseSeeder(ApplicationDbContext context, IAuthService authService)
    {
        _context = context;
        _authService = authService;
    }

    public async Task SeedAsync()
    {
        // check if there is a pending migrations
        var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
        if (pendingMigrations.Any())
        {
            await _context.Database.MigrateAsync();
        }

        // Seed default admin user if no users exist
        if (!await _context.Users.AnyAsync())
        {
            var adminUser = new User
            {
                Username = "admin",
                Password = _authService.HashPassword("admin123"),
                Email = "<EMAIL>",
                Role = "Admin",
                Phone = "1234567890"
            };

            _context.Users.Add(adminUser);
            await _context.SaveChangesAsync();
        }

        // Seed default departments if no departments exist
        if (!await _context.Departments.AnyAsync())
        {
            var departments = new List<Department>
            {
                new Department
                {
                    Name = "قسم إدارة الأصول",
                    Description = "قسم مسؤول عن إدارة وتتبع الأصول في المستشفى",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Department
                {
                    Name = "قسم المشتريات",
                    Description = "قسم مسؤول عن شراء وتوريد المواد والمعدات",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Department
                {
                    Name = "قسم الصيانة",
                    Description = "قسم مسؤول عن صيانة الأجهزة والمعدات الطبية",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Department
                {
                    Name = "قسم المخازن",
                    Description = "قسم مسؤول عن إدارة المخازن والمستودعات",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            };

            _context.Departments.AddRange(departments);
            await _context.SaveChangesAsync();
        }

        // Seed default nationalities if no nationalities exist
        if (!await _context.Nationalities.AnyAsync())
        {
            var nationalities = new List<Nationality>
            {
                new Nationality
                {
                    Name = "سعودي",
                    Code = "SA",
                    Description = "الجنسية السعودية",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Nationality
                {
                    Name = "مصري",
                    Code = "EG",
                    Description = "الجنسية المصرية",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Nationality
                {
                    Name = "أردني",
                    Code = "JO",
                    Description = "الجنسية الأردنية",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Nationality
                {
                    Name = "لبناني",
                    Code = "LB",
                    Description = "الجنسية اللبنانية",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Nationality
                {
                    Name = "سوري",
                    Code = "SY",
                    Description = "الجنسية السورية",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Nationality
                {
                    Name = "عراقي",
                    Code = "IQ",
                    Description = "الجنسية العراقية",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Nationality
                {
                    Name = "كويتي",
                    Code = "KW",
                    Description = "الجنسية الكويتية",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Nationality
                {
                    Name = "إماراتي",
                    Code = "AE",
                    Description = "الجنسية الإماراتية",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            };

            _context.Nationalities.AddRange(nationalities);
            await _context.SaveChangesAsync();
        }

        // Seed default qualifications if no qualifications exist
        if (!await _context.Qualifications.AnyAsync())
        {
            var qualifications = new List<Qualification>
            {
                new Qualification { Name = "دكتوراه" },
                new Qualification { Name = "ماجستير" },
                new Qualification { Name = "بكالوريوس" },
                new Qualification { Name = "دبلوم" },
                new Qualification { Name = "ثانوية عامة" },
                new Qualification { Name = "متوسط" },
                new Qualification { Name = "ابتدائي" }
            };

            _context.Qualifications.AddRange(qualifications);
            await _context.SaveChangesAsync();
        }

        // Seed default employment types if no employment types exist
        if (!await _context.EmploymentTypes.AnyAsync())
        {
            var employmentTypes = new List<EmploymentType>
            {
                new EmploymentType { Name = "دوام كامل" },
                new EmploymentType { Name = "دوام جزئي" },
                new EmploymentType { Name = "عقد مؤقت" },
                new EmploymentType { Name = "عقد دائم" },
                new EmploymentType { Name = "متعاون" },
                new EmploymentType { Name = "متدرب" }
            };

            _context.EmploymentTypes.AddRange(employmentTypes);
            await _context.SaveChangesAsync();
        }

        // Seed default job types if no job types exist
        if (!await _context.JobTypes.AnyAsync())
        {
            var jobTypes = new List<JobType>
            {
                new JobType { Name = "مدير" },
                new JobType { Name = "مشرف" },
                new JobType { Name = "موظف" },
                new JobType { Name = "فني" },
                new JobType { Name = "مهندس" },
                new JobType { Name = "طبيب" },
                new JobType { Name = "ممرض" },
                new JobType { Name = "محاسب" },
                new JobType { Name = "سكرتير" },
                new JobType { Name = "سائق" }
            };

            _context.JobTypes.AddRange(jobTypes);
            await _context.SaveChangesAsync();
        }

        // Seed default order types if no order types exist
        if (!await _context.OrdersTypes.AnyAsync())
        {
            var orderTypes = new List<OrdersType>
            {
                new OrdersType { Name = "إجازة اعتيادية / سنوية"},
                new OrdersType { Name = "إجازة استثنائية"},
                new OrdersType { Name = "إجازة اضطرارية"},
                new OrdersType { Name = "إجازة تعويضية"},
                new OrdersType { Name = "إجازة مرضية"},
                new OrdersType { Name = "إجازة زواج"},
                new OrdersType { Name = "إجازة مولود جديد"},
                new OrdersType { Name = "إجازة وفاة أقارب"},
                new OrdersType { Name = "طلب نقل"},
                new OrdersType { Name = "استقالة" },
                new OrdersType { Name = "تقاعد" },
                new OrdersType { Name = "طلب بدل أثاث" },
                new OrdersType { Name = "طلب شهادة" },
                new OrdersType { Name = "طلب إيفاد" },
                new OrdersType { Name = "أخرى" }
            };

            _context.OrdersTypes.AddRange(orderTypes);
            await _context.SaveChangesAsync();
        }

        // Seed sample orders if no orders exist
        if (!await _context.OrdersTables.AnyAsync())
        {
            var sampleOrders = new List<OrdersTable>
            {
                new OrdersTable
                {
                    CreatedAt = DateTime.Now.AddDays(-30),
                    OrderType = "إجازة اعتيادية / سنوية",
                    EmployeeName = "أحمد محمد علي",
                    JobTitle = "طبيب",
                    EmployeeNumber = "EMP001",
                    CivilRecord = "1234567890",
                    Nationality = "سعودي",
                    MobileNumber = "+966501234567",
                    EmploymentType = "دوام كامل",
                    Qualification = "دكتوراه",
                    Department = "قسم إدارة الأصول",
                    Details = "طلب إجازة سنوية لمدة 15 يوم للراحة والاستجمام",
                    OrderStatus = OrderStatus.DM,
                    File1Url = "sample_document_1.pdf",
                    File2Url = "sample_document_2.pdf"
                },
                new OrdersTable
                {
                    CreatedAt = DateTime.Now.AddDays(-25),
                    OrderType = "طلب نقل",
                    EmployeeName = "فاطمة أحمد حسن",
                    JobTitle = "ممرض",
                    EmployeeNumber = "EMP002",
                    CivilRecord = "0987654321",
                    Nationality = "مصري",
                    MobileNumber = "+966502345678",
                    EmploymentType = "دوام كامل",
                    Qualification = "بكالوريوس",
                    Department = "قسم المشتريات",
                    Details = "طلب نقل من قسم المشتريات إلى قسم الصيانة لأسباب شخصية",
                    OrderStatus = OrderStatus.DM,
                    File1Url = "transfer_request.pdf"
                },
                new OrdersTable
                {
                    CreatedAt = DateTime.Now.AddDays(-20),
                    OrderType = "إجازة زواج",
                    EmployeeName = "خالد عبدالله السعد",
                    JobTitle = "مهندس",
                    EmployeeNumber = "EMP003",
                    CivilRecord = "1122334455",
                    Nationality = "سعودي",
                    MobileNumber = "+966503456789",
                    EmploymentType = "عقد دائم",
                    Qualification = "ماجستير",
                    Department = "قسم الصيانة",
                    Details = "طلب إجازة زواج لمدة 10 أيام",
                    OrderStatus = OrderStatus.DM,
                    File1Url = "marriage_certificate.pdf",
                    File2Url = "wedding_invitation.pdf"
                },
                new OrdersTable
                {
                    CreatedAt = DateTime.Now.AddDays(-15),
                    OrderType = "طلب شهادة",
                    EmployeeName = "نورا سعد العتيبي",
                    JobTitle = "محاسب",
                    EmployeeNumber = "EMP004",
                    CivilRecord = "5544332211",
                    Nationality = "سعودي",
                    MobileNumber = "+966504567890",
                    EmploymentType = "دوام كامل",
                    Qualification = "بكالوريوس",
                    Department = "قسم المخازن",
                    Details = "طلب شهادة خبرة للعمل في مؤسسة أخرى",
                    OrderStatus = OrderStatus.DM,
                    File1Url = "experience_request.pdf"
                },
                new OrdersTable
                {
                    CreatedAt = DateTime.Now.AddDays(-10),
                    OrderType = "إجازة مرضية",
                    EmployeeName = "علي حسن محمد",
                    JobTitle = "فني",
                    EmployeeNumber = "EMP005",
                    CivilRecord = "**********",
                    Nationality = "أردني",
                    MobileNumber = "+966505678901",
                    EmploymentType = "دوام كامل",
                    Qualification = "دبلوم",
                    Department = "قسم إدارة الأصول",
                    Details = "طلب إجازة مرضية لمدة 7 أيام بسبب عملية جراحية",
                    OrderStatus = OrderStatus.DM,
                    File1Url = "medical_report.pdf",
                    File2Url = "doctor_prescription.pdf"
                },
                new OrdersTable
                {
                    CreatedAt = DateTime.Now.AddDays(-5),
                    OrderType = "طلب بدل أثاث",
                    EmployeeName = "سارة محمد الزهراني",
                    JobTitle = "سكرتير",
                    EmployeeNumber = "EMP006",
                    CivilRecord = "**********",
                    Nationality = "سعودي",
                    MobileNumber = "+966506789012",
                    EmploymentType = "دوام جزئي",
                    Qualification = "ثانوية عامة",
                    Department = "قسم المشتريات",
                    Details = "طلب بدل أثاث مكتبي جديد لتحسين بيئة العمل",
                    OrderStatus = OrderStatus.DM,
                    File1Url = "furniture_request.pdf"
                },
                new OrdersTable
                {
                    CreatedAt = DateTime.Now.AddDays(-3),
                    OrderType = "إجازة مولود جديد",
                    EmployeeName = "محمد عبدالرحمن القحطاني",
                    JobTitle = "مدير",
                    EmployeeNumber = "EMP007",
                    CivilRecord = "4433221100",
                    Nationality = "سعودي",
                    MobileNumber = "+966507890123",
                    EmploymentType = "عقد دائم",
                    Qualification = "دكتوراه",
                    Department = "قسم المخازن",
                    Details = "طلب إجازة مولود جديد لمدة 3 أيام",
                    OrderStatus = OrderStatus.DM,
                    File1Url = "birth_certificate.pdf"
                },
                new OrdersTable
                {
                    CreatedAt = DateTime.Now.AddDays(-1),
                    OrderType = "طلب إيفاد",
                    EmployeeName = "ليلى أحمد العلي",
                    JobTitle = "مشرف",
                    EmployeeNumber = "EMP008",
                    CivilRecord = "3322110099",
                    Nationality = "لبناني",
                    MobileNumber = "+966508901234",
                    EmploymentType = "دوام كامل",
                    Qualification = "ماجستير",
                    Department = "قسم الصيانة",
                    Details = "طلب إيفاد لحضور مؤتمر تقني في الرياض لمدة يومين",
                    OrderStatus = OrderStatus.DM,
                    File1Url = "conference_invitation.pdf",
                    File2Url = "travel_request.pdf"
                }
            };

            _context.OrdersTables.AddRange(sampleOrders);
            await _context.SaveChangesAsync();
        }

        // Seed sample employees if no employees exist
        if (!await _context.Employees.AnyAsync())
        {
            var sampleEmployees = new List<Employee>
            {
                new Employee
                {
                    Name = "أحمد محمد علي",
                    Job = "طبيب",
                    EmployeeNumber = "EMP001",
                    CivilNumber = "1234567890",
                    Nationality = "سعودي",
                    Mobile = "+966501234567",
                    EmploymentType = "دوام كامل",
                    Qualification = "دكتوراه"
                },
                new Employee
                {
                    Name = "فاطمة أحمد حسن",
                    Job = "ممرض",
                    EmployeeNumber = "EMP002",
                    CivilNumber = "0987654321",
                    Nationality = "مصري",
                    Mobile = "+966502345678",
                    EmploymentType = "دوام كامل",
                    Qualification = "بكالوريوس"
                },
                new Employee
                {
                    Name = "خالد عبدالله السعد",
                    Job = "مهندس",
                    EmployeeNumber = "EMP003",
                    CivilNumber = "1122334455",
                    Nationality = "سعودي",
                    Mobile = "+966503456789",
                    EmploymentType = "عقد دائم",
                    Qualification = "ماجستير"
                },
                new Employee
                {
                    Name = "نورا سعد العتيبي",
                    Job = "محاسب",
                    EmployeeNumber = "EMP004",
                    CivilNumber = "5544332211",
                    Nationality = "سعودي",
                    Mobile = "+966504567890",
                    EmploymentType = "دوام كامل",
                    Qualification = "بكالوريوس"
                },
                new Employee
                {
                    Name = "علي حسن محمد",
                    Job = "فني",
                    EmployeeNumber = "EMP005",
                    CivilNumber = "**********",
                    Nationality = "أردني",
                    Mobile = "+966505678901",
                    EmploymentType = "دوام كامل",
                    Qualification = "دبلوم"
                },
                new Employee
                {
                    Name = "سارة محمد الزهراني",
                    Job = "سكرتير",
                    EmployeeNumber = "EMP006",
                    CivilNumber = "**********",
                    Nationality = "سعودي",
                    Mobile = "+966506789012",
                    EmploymentType = "دوام جزئي",
                    Qualification = "ثانوية عامة"
                },
                new Employee
                {
                    Name = "محمد عبدالرحمن القحطاني",
                    Job = "مدير",
                    EmployeeNumber = "EMP007",
                    CivilNumber = "4433221100",
                    Nationality = "سعودي",
                    Mobile = "+966507890123",
                    EmploymentType = "عقد دائم",
                    Qualification = "دكتوراه"
                },
                new Employee
                {
                    Name = "ليلى أحمد العلي",
                    Job = "مشرف",
                    EmployeeNumber = "EMP008",
                    CivilNumber = "3322110099",
                    Nationality = "لبناني",
                    Mobile = "+966508901234",
                    EmploymentType = "دوام كامل",
                    Qualification = "ماجستير"
                }
            };

            _context.Employees.AddRange(sampleEmployees);
            await _context.SaveChangesAsync();
        }
    }
}