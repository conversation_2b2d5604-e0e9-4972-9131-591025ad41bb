using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;

namespace OrderFlowCore.Web.Controllers;

[Authorize]
public class DashboardController : Controller
{
    private readonly IDashboardService _dashboardService;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(IDashboardService dashboardService, ILogger<DashboardController> logger)
    {
        _dashboardService = dashboardService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            _logger.LogInformation("Loading dashboard for user: {Username}", User.Identity?.Name);

            var statisticsResult = await _dashboardService.GetDashboardStatisticsAsync();

            var viewModel = new DashboardViewModel
            {
                Username = User.Identity?.Name ?? "غير محدد",
                Role = User.FindFirstValue(ClaimTypes.Role) ?? "غير محدد",
                Email = User.FindFirstValue("Email") ?? "غير محدد"
            };

            if (statisticsResult.IsSuccess)
            {
                viewModel.Statistics = statisticsResult.Data ?? new DashboardStatisticsDto();
            }
            else
            {
                viewModel.HasError = true;
                viewModel.ErrorMessage = statisticsResult.Message;
                _logger.LogWarning("Failed to load dashboard statistics: {Error}", statisticsResult.Message);
            }

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard for user: {Username}", User.Identity?.Name);

            var errorViewModel = new DashboardViewModel
            {
                Username = User.Identity?.Name ?? "غير محدد",
                Role = User.FindFirstValue(ClaimTypes.Role) ?? "غير محدد",
                Email = User.FindFirstValue("Email") ?? "غير محدد",
                HasError = true,
                ErrorMessage = "حدث خطأ أثناء تحميل لوحة التحكم"
            };

            return View(errorViewModel);
        }
    }

}