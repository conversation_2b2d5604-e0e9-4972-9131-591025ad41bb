using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using OrderFlowCore.Application.Interfaces.Repositories;

namespace OrderFlowCore.Web.Controllers;

[Authorize]
public class DashboardController : Controller
{
    

    public DashboardController()
    {
        
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            
            ViewBag.Username = User.Identity?.Name;
            ViewBag.Role = User.FindFirstValue(ClaimTypes.Role);
            ViewBag.Email = User.FindFirstValue("Email");

            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Username = User.Identity?.Name;
            ViewBag.Role = User.FindFirstValue(ClaimTypes.Role);
            ViewBag.Email = User.FindFirstValue("Email");
            
            return View();
        }
    }

}