using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories;

public interface IOrderRepository
{
    Task<int> AddAsync(OrdersTable order);
    Task<int> UpdateAsync(OrdersTable order);
    Task<OrdersTable> GetByIdAsync(int id);
    Task<List<OrdersTable>> GetAllAsync();
    Task<List<OrdersTable>> GetPendingOrdersForDirectMangerAsync();
    Task<List<OrdersTable>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId);
    Task<OrdersTable> GetOrderByNumberAsync(string orderNumber);
    Task<List<OrdersTable>> GetOrdersByStatusesAsync(string[] statuses);
}
