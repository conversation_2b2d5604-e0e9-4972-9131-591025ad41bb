@model OrderFlowCore.Web.ViewModels.SupervisorFollowUpViewModel
@{
    ViewBag.Title = "متابعة السجلات الخاصة";
}

<div class="container mt-4">
    <h2 class="mb-4 text-center">متابعة السجلات الخاصة</h2>

    <!-- رسائل النظام -->
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success">@TempData["Message"]</div>
    }
    @if (!string.IsNullOrEmpty(Model.Message))
    {
        <div class="alert alert-success">@Model.Message</div>
    }
    @if (!string.IsNullOrEmpty(Model.Error))
    {
        <div class="alert alert-danger">@Model.Error</div>
    }

    <!-- Dropdown for order numbers and actions -->
    <form asp-action="Index" method="get">
        <div class="row mb-3">
            <div class="col-md-6">
                <label>رقم الطلب:</label>
                <select name="selectedOrderNumber" class="form-control" onchange="this.form.submit()">
                    <option value="">-- اختر رقم الطلب --</option>
                    @foreach (var item in Model.OrderNumbers)
                    {
                        @* <option value="@item.Value" @(item.Value == Model.SelectedOrderNumber ? "selected" : "")>@item.Text</option> *@
                    }
                </select>
            </div>
        </div>
    </form>

    <form asp-action="ConfirmOrder" method="post">
        @Html.AntiForgeryToken()
        <input type="hidden" name="selectedOrderNumber" value="@Model.SelectedOrderNumber" />
        <button type="submit" class="btn btn-success mr-2">اعتماد</button>
    </form>

    <form asp-action="NeedsAction" method="post" class="mt-2">
        @Html.AntiForgeryToken()
        <input type="hidden" name="selectedOrderNumber" value="@Model.SelectedOrderNumber" />
        <div class="row mb-2">
            <div class="col-md-6">
                <input name="actionRequired" class="form-control" placeholder="الإجراءات المطلوبة" />
            </div>
            <div class="col-md-6">
                <button type="submit" class="btn btn-warning">يتطلب إجراءات</button>
            </div>
        </div>
    </form>

    <form asp-action="RejectOrder" method="post" class="mt-2">
        @Html.AntiForgeryToken()
        <input type="hidden" name="selectedOrderNumber" value="@Model.SelectedOrderNumber" />
        <div class="row mb-2">
            <div class="col-md-6">
                <input name="rejectReason" class="form-control" placeholder="سبب الإعادة" />
            </div>
            <div class="col-md-6">
                <button type="submit" class="btn btn-danger">إعادة</button>
            </div>
        </div>
    </form>

    <!-- Order details -->
    @if (Model.OrderDetails != null)
    {
        <div class="card mb-4">
            <div class="card-header">تفاصيل الطلب</div>
            <div class="card-body">
                <p><strong>رقم الطلب:</strong> @Model.OrderDetails.Id</p>
                <p><strong>اسم الموظف:</strong> @Model.OrderDetails.EmployeeName</p>
                <p><strong>نوع الطلب:</strong> @Model.OrderDetails.OrderType</p>
                <p><strong>القسم:</strong> @Model.OrderDetails.Department</p>
                <p><strong>تاريخ الإنشاء:</strong> @Model.OrderDetails.CreatedAt.ToString("yyyy-MM-dd")</p>
                <p><strong>حالة الطلب:</strong> @Model.OrderDetails.OrderStatus</p>
                <!-- ... add more fields as needed ... -->
            </div>
        </div>
    }

    <!-- قسم إضافة سجل متابعة جديد -->
    <div class="card mb-4">
        <div class="card-header">إضافة سجل متابعة جديد</div>
        <div class="card-body">
            <form asp-action="AddFollowUp" method="post" class="form-inline">
                @Html.AntiForgeryToken()
                <div class="form-row align-items-center">
                    <div class="col-auto mb-2">
                        <input name="civilRegistry" class="form-control" placeholder="السجل المدني" required />
                    </div>
                    <div class="col-auto mb-2">
                        <input name="ownerName" class="form-control" placeholder="اسم صاحب الطلب" required />
                    </div>
                    <div class="col-auto mb-2">
                        <input name="specialProcedure" class="form-control" placeholder="الإجراء المطلوب" required />
                    </div>
                    <div class="col-auto mb-2">
                        <button type="submit" class="btn btn-primary">إضافة</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Follow-up records -->
    <div class="card mb-4">
        <div class="card-header">سجلات المتابعة</div>
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>السجل المدني</th>
                        <th>اسم صاحب الطلب</th>
                        <th>الإجراء المطلوب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var rec in Model.FollowUpRecords)
                    {
                        <tr>
                            <td>@rec.CivilRegistry</td>
                            <td>@rec.OwnerName</td>
                            <td>@rec.SpecialProcedure</td>
                            <td>
                                <!-- Edit form (inline, for demo; in production, use modal or separate page) -->
                                <form asp-action="EditFollowUp" method="post" style="display:inline-block">
                                    @Html.AntiForgeryToken()
                                    <input type="hidden" name="civilRegistry" value="@rec.CivilRegistry" />
                                    <input type="text" name="ownerName" value="@rec.OwnerName" class="form-control d-inline-block" style="width:120px" required />
                                    <input type="text" name="specialProcedure" value="@rec.SpecialProcedure" class="form-control d-inline-block" style="width:120px" required />
                                    <button type="submit" class="btn btn-sm btn-primary">تعديل</button>
                                </form>
                                <form asp-action="DeleteFollowUp" method="post" style="display:inline-block; margin-right:5px">
                                    @Html.AntiForgeryToken()
                                    <input type="hidden" name="civilRegistry" value="@rec.CivilRegistry" />
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</button>
                                </form>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>

    <!-- استيراد وتصدير CSV -->
    <div class="mb-4">
        <form asp-action="ImportCsv" method="post" enctype="multipart/form-data" class="d-inline-block mr-2">
            @Html.AntiForgeryToken()
            <input type="file" name="csvFile" accept=".csv" required />
            <button type="submit" class="btn btn-secondary">استيراد CSV</button>
        </form>
        <form asp-action="ExportCsv" method="post" class="d-inline-block">
            @Html.AntiForgeryToken()
            <button type="submit" class="btn btn-info">تصدير CSV</button>
        </form>
    </div>

    <!-- TODO: Add forms for adding/editing follow-up records, import/export CSV, download attachments, etc. -->
</div> 