@model OrderFlowCore.Application.DTOs.JobTypeDto
@{
    ViewData["Title"] = "تعديل نوع الوظيفة";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="container mt-4" dir="rtl">
    <h2 class="mb-4">تعديل نوع الوظيفة</h2>
    <form asp-action="Edit" method="post">
        <input type="hidden" asp-for="Id" />
        <div class="form-group mb-3">
            <label asp-for="Name"></label>
            <input asp-for="Name" class="form-control" />
            <span asp-validation-for="Name" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-primary">حفظ التعديلات</button>
        <a asp-action="Index" class="btn btn-secondary">إلغاء</a>
    </form>
</div>
@section Scripts {
    <partial name="~/Views/Shared/_ValidationScriptsPartial.cshtml" />
    <script>
        $(function () {
            var error = '@TempData["ToastrError"]';
            if (error) toastr.error(error);
        });
    </script>
} 