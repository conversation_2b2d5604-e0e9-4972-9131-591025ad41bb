using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IDepartmentService
{
    Task<ServiceResult<IEnumerable<DepartmentDto>>> GetAllDepartmentsAsync();
    Task<ServiceResult<DepartmentDto>> GetDepartmentByIdAsync(int id);
    Task<ServiceResult> CreateDepartmentAsync(DepartmentDto dto);
    Task<ServiceResult> UpdateDepartmentAsync(DepartmentDto dto);
    Task<ServiceResult> DeleteDepartmentAsync(int id);
}
