using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Web.Controllers;

[Authorize]
public class AccountsController : Controller
{
    private readonly IAccountManagementService _accountManagementService;
    private readonly ILogger<AccountsController> _logger;

    public AccountsController(
        IAccountManagementService accountManagementService,
        ILogger<AccountsController> logger)
    {
        _accountManagementService = accountManagementService;
        _logger = logger;
    }

    public async Task<IActionResult> Index(string editUsername = null)
    {
        var result = await _accountManagementService.GetAccountsDataAsync();
        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction("Index", "Dashboard");
        }
        var dto = result.Data;
        if (!string.IsNullOrEmpty(editUsername))
        {
            var userResult = await _accountManagementService.SearchUsersAsync(editUsername);
            if (userResult.IsSuccess && userResult.Data.Users.Any())
            {
                var user = userResult.Data.Users.FirstOrDefault(u => u.Username == editUsername);
                if (user != null)
                {
                    dto.EditModel = new UserEditDto
                    {
                        Username = user.Username,
                        Email = user.Email,
                        Phone = user.Phone
                    };
                }
            }
        }
        dto.AddModel = new UserCreateDto();
        return View(dto);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateUser(UserCreateDto dto)
    {
        var result = await _accountManagementService.CreateUserAsync(dto);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction("Index");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateUser(UserEditDto dto)
    {
        var result = await _accountManagementService.UpdateUserAsync(dto);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction("Index");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteUser(string username)
    {
        var result = await _accountManagementService.DeleteUserAsync(username);

        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SearchUsers(string searchTerm)
    {
        var result = await _accountManagementService.SearchUsersAsync(searchTerm);

        if (result.IsSuccess)
        {
            return View("Index", result.Data);
        }

        TempData["ErrorMessage"] = result.Message;
        return RedirectToAction(nameof(Index));
    }
}
