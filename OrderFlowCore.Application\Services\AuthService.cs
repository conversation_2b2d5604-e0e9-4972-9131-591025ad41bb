using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using System.Security.Cryptography;
using System.Text;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Services;

public class AuthService : IAuthService
{
    private readonly IUnitOfWork _unitOfWork;

    public AuthService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<ServiceResult> ValidateUserAsync(string username, string password)
    {
        var user = await _unitOfWork.Users.GetByUsernameAsync(username);

        if (user == null)
            return ServiceResult.Failure("Invalid username or password");

        if (!VerifyPassword(password, user.Password))
            return ServiceResult.Failure("Invalid username or password");

        return ServiceResult.Success("User validated successfully");
    }

    public async Task<ServiceResult<UserDto>> GetUserByUsernameAsync(string username)
    {
        var user = await _unitOfWork.Users.GetByUsernameAsync(username);
        
        if (user == null)
            return ServiceResult<UserDto>.Failure("User not found");
            
        return ServiceResult<UserDto>.Success(user);
    }

    public string HashPassword(string password)
    {
        using (var sha256 = SHA256.Create())
        {
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }
    }

    public bool VerifyPassword(string password, string hashedPassword)
    {
        var hashedInput = HashPassword(password);
        return hashedInput == hashedPassword;
    }
} 