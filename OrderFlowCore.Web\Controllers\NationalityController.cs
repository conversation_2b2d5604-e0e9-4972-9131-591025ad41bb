using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;

using Microsoft.Extensions.Logging;

namespace OrderFlowCore.Web.Controllers;

[Authorize]
public class NationalityController : Controller
{
    private readonly INationalityService _nationalityService;
    
    private readonly ILogger<NationalityController> _logger;

    public NationalityController(INationalityService nationalityService, ILogger<NationalityController> logger)
    {
        _nationalityService = nationalityService;
        
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var result = await _nationalityService.GetAllAsync();
        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction("Index", "Home");
        }
        
        return View(result.Data);
    }

    public IActionResult Create()
    {
        return View(new NationalityDto());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(NationalityDto nationality)
    {
        if (!ModelState.IsValid)
        {
            return View(nationality);
        }

        var result = await _nationalityService.CreateAsync(nationality);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
            return RedirectToAction(nameof(Index));
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }

        return View(nationality);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var result = await _nationalityService.GetByIdAsync(id);
        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction(nameof(Index));
        }
        
        return View(result.Data);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(NationalityDto nationality)
    {
        if (ModelState.IsValid)
        {
            var result = await _nationalityService.UpdateAsync(nationality);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
        }
        return View(nationality);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Delete(int id)
    {
        var result = await _nationalityService.DeleteAsync(id);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction(nameof(Index));
    }
} 