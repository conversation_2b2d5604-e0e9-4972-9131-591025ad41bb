@model OrderFlowCore.Web.ViewModels.EmployeeViewModel
@{
    ViewData["Title"] = "تعديل بيانات الموظف";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div>
                    <h1 class="page-title">تعديل بيانات الموظف</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Settings")">الإعدادات</a></li>
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Employee")">إدارة بيانات الموظفين</a></li>
                            <li class="breadcrumb-item active" aria-current="page">تعديل بيانات الموظف</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>تعديل بيانات الموظف
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input type="hidden" asp-for="Id" />
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label"></label>
                                    <input asp-for="Name" class="form-control" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Job" class="form-label"></label>
                                    <select asp-for="Job" class="form-select" asp-items="Model.JobTypes">
                                        <option value="">-- اختر الوظيفة --</option>
                                    </select>
                                    <span asp-validation-for="Job" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmployeeNumber" class="form-label"></label>
                                    <input asp-for="EmployeeNumber" class="form-control" />
                                    <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="CivilNumber" class="form-label"></label>
                                    <input asp-for="CivilNumber" class="form-control" />
                                    <span asp-validation-for="CivilNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Nationality" class="form-label"></label>
                                    <select asp-for="Nationality" class="form-select" asp-items="Model.Nationalities">
                                        <option value="">-- اختر الجنسية --</option>
                                    </select>
                                    <span asp-validation-for="Nationality" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Mobile" class="form-label"></label>
                                    <input asp-for="Mobile" class="form-control" />
                                    <span asp-validation-for="Mobile" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmploymentType" class="form-label"></label>
                                    <select asp-for="EmploymentType" class="form-select" asp-items="Model.EmploymentTypes">
                                        <option value="">-- اختر نوع التوظيف --</option>
                                    </select>
                                    <span asp-validation-for="EmploymentType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Qualification" class="form-label"></label>
                                    <select asp-for="Qualification" class="form-select" asp-items="Model.Qualifications">
                                        <option value="">-- اختر المؤهل --</option>
                                    </select>
                                    <span asp-validation-for="Qualification" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>عودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-2"></i>حفظ التعديلات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 