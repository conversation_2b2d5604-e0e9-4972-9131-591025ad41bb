using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

using Microsoft.Extensions.Logging;

namespace OrderFlowCore.Web.Controllers
{
    public class OrderController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly IEmployeeService _employeeService;
        private readonly ILogger<OrderController> _logger;

        public OrderController(IOrderService orderService, IEmployeeService employeeService, ILogger<OrderController> logger)
        {
            _orderService = orderService;
            _employeeService = employeeService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> New()
        {
            var dropdownResult = await _orderService.GetDropdownDataAsync();
            if (!dropdownResult.IsSuccess)
            {
                TempData["ErrorMessage"] = dropdownResult.Message;
                return RedirectToAction("Index", "Home");
            }
            
            var viewModel = new OrderNewViewModel();
            PopulateDropdowns(viewModel, dropdownResult.Data);
            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> New(OrderNewViewModel model)
        {
            if (!ModelState.IsValid)
            {
                var dropdownResult = await _orderService.GetDropdownDataAsync();
                if (dropdownResult.IsSuccess)
                {
                    PopulateDropdowns(model, dropdownResult.Data);
                }
                return View(model);
            }

            var orderDto = OrderNewViewModel.ToDto(model);
            // معالجة المرفقات
            orderDto.Attachments = new List<byte[]>();
            if (model.Attachments != null)
            {
                foreach (var file in model.Attachments)
                {
                    if (file != null && file.Length > 0)
                    {
                        using var ms = new MemoryStream();
                        await file.CopyToAsync(ms);
                        orderDto.Attachments.Add(ms.ToArray());
                    }
                }
            }
            
            var result = await _orderService.CreateOrderAsync(orderDto);
            if (result.IsSuccess)
            {
                TempData["OrderSuccess"] = true;
                TempData["OrderId"] = result.Data?.Id;
                return RedirectToAction(nameof(New));
            }
            else
            {
                ModelState.AddModelError("", result.Message);
                var dropdownResult = await _orderService.GetDropdownDataAsync();
                if (dropdownResult.IsSuccess)
                {
                    PopulateDropdowns(model, dropdownResult.Data);
                }
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> Details(int id)
        {
            var result = await _orderService.GetOrderDetailsAsync(id);
            if (!result.IsSuccess)
            {
                return NotFound();
            }
            
            var viewModel = OrderDetailsViewModel.FromDto(result.Data);
            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UploadAttachment(int orderId, IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("لم يتم اختيار ملف");
            }
            
            using var ms = new MemoryStream();
            await file.CopyToAsync(ms);
            var fileData = ms.ToArray();
            
            var result = await _orderService.UploadAttachmentAsync(orderId, fileData, file.FileName);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            
            return RedirectToAction(nameof(Details), new { id = orderId });
        }

        [HttpPost]
        public async Task<IActionResult> UploadFile()
        {
            try
            {
                var file = Request.Form.Files["file"];
                var orderId = int.Parse(Request.Form["orderId"]);
                var fileNumber = int.Parse(Request.Form["fileNumber"]);

                if (file == null || file.Length == 0)
                {
                    return Json(new { success = false, message = "لم يتم اختيار ملف" });
                }

                // Validate file type
                if (file.ContentType != "application/pdf")
                {
                    return Json(new { success = false, message = "يجب أن يكون الملف بصيغة PDF فقط" });
                }

                // Validate file size (5MB = 5242880 bytes)
                if (file.Length > 5242880)
                {
                    return Json(new { success = false, message = "حجم الملف يجب أن يكون أقل من 5 ميجابايت" });
                }

                // Read file data
                using var ms = new MemoryStream();
                await file.CopyToAsync(ms);
                var fileData = ms.ToArray();

                // Generate unique filename
                var fileName = $"file{fileNumber}.pdf";

                // Upload file using the service
                var result = await _orderService.UploadAttachmentAsync(orderId, fileData, fileName);
                
                if (result.IsSuccess)
                {
                    return Json(new { 
                        success = true, 
                        message = "تم رفع الملف بنجاح",
                        fileName = fileName
                    });
                }
                else
                {
                    return Json(new { 
                        success = false, 
                        message = result.Message ?? "حدث خطأ أثناء رفع الملف"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file");
                return Json(new { 
                    success = false, 
                    message = "حدث خطأ غير متوقع أثناء رفع الملف"
                });
            }
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int id)
        {
            var result = await _orderService.DownloadAttachmentsZipAsync(id);
            if (!result.IsSuccess)
            {
                return NotFound(result.Message);
            }
            
            return File(result.Data, "application/zip", $"attachments_order_{id}.zip");
        }

        [HttpGet]
        public async Task<IActionResult> SearchEmployee(string civilRecord)
        {
            if (string.IsNullOrWhiteSpace(civilRecord))
            {
                return Json(new { success = false, message = "يرجى إدخال السجل المدني" });
            }

            var result = await _employeeService.GetByCivilNumberAsync(civilRecord.Trim());
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = "لم يتم العثور على الموظف" });
            }

            var employee = result.Data;
            return Json(new { 
                success = true, 
                employee = new {
                    name = employee.Name,
                    job = employee.Job,
                    employeeNumber = employee.EmployeeNumber,
                    civilNumber = employee.CivilNumber,
                    nationality = employee.Nationality,
                    mobile = employee.Mobile,
                    employmentType = employee.EmploymentType,
                    qualification = employee.Qualification
                }
            });
        }

        private void PopulateDropdowns(OrderNewViewModel model, DropdownDataDto dropdownData)
        {
            model.Departments = dropdownData.Departments.Select(d => new SelectListItem { Value = d.Value, Text = d.Text }).ToList();
            model.JobTitles = dropdownData.JobTitles.Select(j => new SelectListItem { Value = j.Value, Text = j.Text }).ToList();
            model.Nationalities = dropdownData.Nationalities.Select(n => new SelectListItem { Value = n.Value, Text = n.Text }).ToList();
            model.EmploymentTypes = dropdownData.EmploymentTypes.Select(e => new SelectListItem { Value = e.Value, Text = e.Text }).ToList();
            model.Qualifications = dropdownData.Qualifications.Select(q => new SelectListItem { Value = q.Value, Text = q.Text }).ToList();
            model.OrderTypes = dropdownData.OrderTypes.Select(o => new SelectListItem { Value = o.Value, Text = o.Text }).ToList();
        }
    }
} 