﻿function fetchUnreadNotifications() {
    $.ajax({
        url: '/Notification/GetUnreadNotifications',
        type: 'GET',
        dataType: 'json',
        success: function (response) {
            var $dropdown = $('#notification-list');
            var $badge = $('#notification-badge');

            $dropdown.empty();

            if (response.success) {
                // Update the unread count badge
                if (response.count > 0) {
                    $badge.text(response.count).show();
                } else {
                    $badge.hide();
                }

                // Render notifications
                if (response.notifications && response.notifications.length > 0) {
                    response.notifications.forEach(function (notif) {
                        // Create a notification item with better structure and action buttons
                        var notificationItem = $('<div class="notification-item unread"></div>');

                        // Content container
                        var contentContainer = $('<div class="notification-content"></div>');
                        contentContainer.append('<h6>' + notif.title + '</h6>');
                        contentContainer.append('<p>' + notif.message + '</p>');
                        contentContainer.append('<small>' + notif.timeAgo + '</small>');

                        // Action buttons container
                        var actionsContainer = $('<div class="notification-actions"></div>');

                        // Mark as read button
                        var markAsReadForm = $('<form action="/Notification/MarkAsRead/' + notif.id + '" method="post"></form>');
                        var markAsReadButton = $('<button type="submit" class="btn btn-sm btn-outline-primary" title="تحديد كمقروء"><i class="bi bi-check"></i></button>');
                        markAsReadForm.append(markAsReadButton);
                        actionsContainer.append(markAsReadForm);

                        // View button (if action URL exists)
                        if (notif.actionUrl) {
                            var viewButton = $('<a href="' + notif.actionUrl + '" class="btn btn-sm btn-outline-info" title="عرض"><i class="bi bi-eye"></i> عرض</a>');
                            actionsContainer.append(viewButton);
                        }

                        // Add content and actions to the notification item
                        notificationItem.append(contentContainer);
                        notificationItem.append(actionsContainer);

                        // Add the notification item to the dropdown
                        $dropdown.append(notificationItem);

                        // Add a divider after each notification except the last one
                        if (response.notifications.indexOf(notif) < response.notifications.length - 1) {
                            $dropdown.append('<div class="dropdown-divider"></div>');
                        }
                    });
                } else {
                    $dropdown.append('<div class="p-3 text-center text-muted">لا توجد إشعارات جديدة</div>');
                }
            } else {
                $dropdown.append('<div class="p-3 text-center text-danger">خطأ: ' + (response.error || 'تعذر جلب الإشعارات') + '</div>');
                $badge.hide();
            }
        },
        error: function () {
            $('#notification-list').html('<div class="p-3 text-center text-danger">حدث خطأ أثناء جلب الإشعارات.</div>');
            $('#notification-badge').hide();
        }
    });
}

// Function to show a SweetAlert notification
function showNotificationAlert(title, message, type = 'info') {
    Swal.fire({
        title: title,
        html: message,
        icon: type,
        confirmButtonText: 'موافق',
        confirmButtonColor: '#3085d6',
        customClass: {
            confirmButton: 'btn btn-primary'
        }
    });
}


