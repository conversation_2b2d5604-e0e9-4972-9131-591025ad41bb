using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IOrdersTypeService
{
    Task<ServiceResult<IEnumerable<OrdersTypeDto>>> GetAllAsync();
    Task<ServiceResult<OrdersTypeDto>> GetByIdAsync(int id);
    Task<ServiceResult> CreateAsync(OrdersTypeDto dto);
    Task<ServiceResult> UpdateAsync(OrdersTypeDto dto);
    Task<ServiceResult> DeleteAsync(int id);
}
