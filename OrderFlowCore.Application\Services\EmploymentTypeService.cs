using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Services
{
    public class EmploymentTypeService : IEmploymentTypeService
    {
        private readonly IUnitOfWork _unitOfWork;
        public EmploymentTypeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<IEnumerable<EmploymentTypeDto>>> GetAllAsync()
        {
            try
            {
                var employmentTypes = await _unitOfWork.EmploymentTypes.GetAllAsync();
                return ServiceResult<IEnumerable<EmploymentTypeDto>>.Success(employmentTypes);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<EmploymentTypeDto>>.Failure($"Error retrieving employment types: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<EmploymentTypeDto>> GetByIdAsync(int id)
        {
            try
            {
                var employmentType = await _unitOfWork.EmploymentTypes.GetByIdAsync(id);
                if (employmentType == null)
                    return ServiceResult<EmploymentTypeDto>.Failure("Employment type not found");
                    
                return ServiceResult<EmploymentTypeDto>.Success(employmentType);
            }
            catch (Exception ex)
            {
                return ServiceResult<EmploymentTypeDto>.Failure($"Error retrieving employment type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(EmploymentTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.EmploymentTypes.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Employment type created successfully");
                else
                    return ServiceResult.Failure("Failed to create employment type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error creating employment type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(EmploymentTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.EmploymentTypes.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Employment type updated successfully");
                else
                    return ServiceResult.Failure("Failed to update employment type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error updating employment type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.EmploymentTypes.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Employment type deleted successfully");
                else
                    return ServiceResult.Failure("Failed to delete employment type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting employment type: {ex.Message}");
            }
        }
        
        public async Task<bool> ExistsAsync(int id) => await _unitOfWork.EmploymentTypes.ExistsAsync(id);
    }
} 