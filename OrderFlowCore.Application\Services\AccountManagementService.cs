using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using System.Security.Cryptography;
using System.Text;

namespace OrderFlowCore.Application.Services;

public class AccountManagementService : IAccountManagementService
{
    private readonly IUserService _userService;
    private readonly ILogger<AccountManagementService> _logger;

    public AccountManagementService(
        IUserService userService,
        ILogger<AccountManagementService> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    public async Task<ServiceResult<AccountsDto>> GetAccountsDataAsync()
    {
        try
        {
            var dto = new AccountsDto();
            await LoadAccountsData(dto);
            return ServiceResult<AccountsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading accounts data");
            return ServiceResult<AccountsDto>.Failure("حدث خطأ أثناء تحميل بيانات الحسابات");
        }
    }

    public async Task<ServiceResult> CreateUserAsync(UserCreateDto dto)
    {
        try
        {
            // Check if user exists
            var existingUserResult = await _userService.GetUserByUsernameAsync(dto.Username);
            if (existingUserResult.IsSuccess && existingUserResult.Data != null)
            {
                return ServiceResult.Failure("اسم المستخدم موجود مسبقاً");
            }

            // Validate email if provided
            if (!string.IsNullOrEmpty(dto.Email) && !IsValidEmail(dto.Email))
            {
                return ServiceResult.Failure("البريد الإلكتروني غير صحيح");
            }

            // Create user DTO
            var userDto = new UserDto
            {
                Username = dto.Username,
                Email = dto.Email,
                Phone = dto.Phone,
                Role = dto.Permission,
                Password = HashPassword(dto.Password)
            };

            var result = await _userService.CreateUserAsync(userDto);
            if (result.IsSuccess)
            {
                return ServiceResult.Success("تم إضافة المستخدم بنجاح");
            }

            return ServiceResult.Failure(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user {Username}", dto.Username);
            return ServiceResult.Failure("حدث خطأ أثناء إضافة المستخدم");
        }
    }

    public async Task<ServiceResult> UpdateUserAsync(UserEditDto dto)
    {
        try
        {
            // Validate email if provided
            if (!string.IsNullOrEmpty(dto.Email) && !IsValidEmail(dto.Email))
            {
                return ServiceResult.Failure("البريد الإلكتروني غير صحيح");
            }

            // Get existing user
            var existingUserResult = await _userService.GetUserByUsernameAsync(dto.Username);
            if (!existingUserResult.IsSuccess || existingUserResult.Data == null)
            {
                return ServiceResult.Failure("المستخدم غير موجود");
            }

            var userDto = existingUserResult.Data;
            userDto.Email = dto.Email;
            userDto.Phone = dto.Phone;

            var result = await _userService.UpdateUserAsync(userDto);
            if (result.IsSuccess)
            {
                return ServiceResult.Success("تم تحديث المستخدم بنجاح");
            }

            return ServiceResult.Failure(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {Username}", dto.Username);
            return ServiceResult.Failure("حدث خطأ أثناء تحديث المستخدم");
        }
    }

    public async Task<ServiceResult> DeleteUserAsync(string username)
    {
        try
        {
            if (username == "Super Admin")
            {
                return ServiceResult.Failure("لا يمكن حذف المستخدم الرئيسي");
            }

            var result = await _userService.DeleteUserAsync(username);
            if (result.IsSuccess)
            {
                return ServiceResult.Success("تم حذف المستخدم بنجاح");
            }

            return ServiceResult.Failure(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {Username}", username);
            return ServiceResult.Failure("حدث خطأ أثناء حذف المستخدم");
        }
    }

    public async Task<ServiceResult<AccountsDto>> SearchUsersAsync(string searchTerm)
    {
        try
        {
            var dto = new AccountsDto
            {
                UserSearchTerm = searchTerm
            };

            if (!string.IsNullOrEmpty(searchTerm))
            {
                var usersResult = await _userService.GetAllUsersAsync();
                if (usersResult.IsSuccess)
                {
                    dto.Users = usersResult.Data?
                        .Where(u => u.Username != "Super Admin" &&
                                  u.Username.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        .ToList() ?? new List<UserDto>();
                }
            }
            else
            {
                await LoadAccountsData(dto);
            }

            dto.AvailablePermissions = GetAvailablePermissions();

            return ServiceResult<AccountsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching users with term {SearchTerm}", searchTerm);
            return ServiceResult<AccountsDto>.Failure("حدث خطأ أثناء البحث");
        }
    }

    public List<string> GetAvailablePermissions()
    {
        return new List<string>
        {
            "مدير الموارد البشرية",
            "منسق الموارد البشرية",
            "مساعد المدير للخدمات الطبية",
            "مساعد المدير لخدمات التمريض",
            "مساعد المدير للخدمات الإدارية والتشغيل",
            "مساعد المدير للموارد البشرية",
            "مدير حسابات",
            "مشرف خدمات الموظفين",
            "مشرف إدارة تخطيط الموارد البشرية",
            "مشرف إدارة تقنية المعلومات",
            "مشرف مراقبة الدوام",
            "مشرف السجلات الطبية",
            "مشرف إدارة الرواتب والاستحقاقات",
            "مشرف إدارة القانونية والالتزام",
            "مشرف خدمات الموارد البشرية",
            "مشرف إدارة الإسكان",
            "مشرف قسم الملفات",
            "مشرف العيادات الخارجية",
            "مشرف التأمينات الاجتماعية",
            "مشرف وحدة مراقبة المخزون",
            "مشرف إدارة تنمية الإيرادات",
            "مشرف إدارة الأمن و السلامة",
            "مشرف الطب الاتصالي"
        };
    }

    public bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address.Equals(email, StringComparison.OrdinalIgnoreCase) &&
                   email.EndsWith("@moh.gov.sa", StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }

    public string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
        return Convert.ToBase64String(hashedBytes);
    }

    private async Task LoadAccountsData(AccountsDto dto)
    {
        var usersResult = await _userService.GetAllUsersAsync();
        if (usersResult.IsSuccess)
        {
            dto.Users = usersResult.Data?.Where(u => u.Username != "Super Admin").ToList() ?? new List<UserDto>();
        }
        dto.AvailablePermissions = GetAvailablePermissions();
    }
}
