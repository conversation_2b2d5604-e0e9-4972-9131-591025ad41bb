using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Services
{
    public class OrdersTypeService : IOrdersTypeService
    {
        private readonly IUnitOfWork _unitOfWork;
        public OrdersTypeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<IEnumerable<OrdersTypeDto>>> GetAllAsync()
        {
            try
            {
                var ordersTypes = await _unitOfWork.OrdersTypes.GetAllAsync();
                return ServiceResult<IEnumerable<OrdersTypeDto>>.Success(ordersTypes);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OrdersTypeDto>>.Failure($"Error retrieving orders types: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<OrdersTypeDto>> GetByIdAsync(int id)
        {
            try
            {
                var ordersType = await _unitOfWork.OrdersTypes.GetByIdAsync(id);
                if (ordersType == null)
                    return ServiceResult<OrdersTypeDto>.Failure("Orders type not found");
                    
                return ServiceResult<OrdersTypeDto>.Success(ordersType);
            }
            catch (Exception ex)
            {
                return ServiceResult<OrdersTypeDto>.Failure($"Error retrieving orders type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(OrdersTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.OrdersTypes.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Orders type created successfully");
                else
                    return ServiceResult.Failure("Failed to create orders type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error creating orders type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(OrdersTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.OrdersTypes.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Orders type updated successfully");
                else
                    return ServiceResult.Failure("Failed to update orders type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error updating orders type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.OrdersTypes.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Orders type deleted successfully");
                else
                    return ServiceResult.Failure("Failed to delete orders type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting orders type: {ex.Message}");
            }
        }
    }
} 