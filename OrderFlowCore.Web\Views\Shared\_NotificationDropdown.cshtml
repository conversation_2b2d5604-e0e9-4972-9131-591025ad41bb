<div class="dropdown">
    <a class="nav-link dropdown-toggle" href="#" id="notificationIcon" role="button"
       data-bs-toggle="dropdown" data-bs-auto-close="true" aria-expanded="false">
        <i class="fas fa-bell"></i>
        <span class="icon-badge" id="notification-badge">0</span>
    </a>
    <div class="dropdown-menu dropdown-menu-start notification-dropdown" aria-labelledby="notificationIcon">
        <div class="dropdown-header d-flex justify-content-between align-items-center">
            <span>الإشعارات</span>
            <div>
                <form asp-controller="Notification" asp-action="MarkAllAsRead" method="post" class="d-inline">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-sm btn-link text-decoration-none">تحديد الكل كمقروء</button>
                </form>
            </div>
        </div>
        <div class="dropdown-divider"></div>
        <div id="notification-list">
            <div class="p-2 text-center">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mb-0">جاري تحميل الإشعارات...</p>
            </div>
        </div>
        <div class="dropdown-divider"></div>
        <a asp-controller="Notification" asp-action="Index" class="dropdown-item text-center">
            عرض جميع الإشعارات
        </a>
    </div>
</div>
