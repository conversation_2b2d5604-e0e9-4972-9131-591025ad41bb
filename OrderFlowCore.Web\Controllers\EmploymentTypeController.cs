using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class EmploymentTypeController : Controller
    {
        private readonly IEmploymentTypeService _employmentTypeService;
        private readonly ILogger<EmploymentTypeController> _logger;

        public EmploymentTypeController(IEmploymentTypeService employmentTypeService, ILogger<EmploymentTypeController> logger)
        {
            _employmentTypeService = employmentTypeService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var result = await _employmentTypeService.GetAllAsync();
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Home");
            }
            
            return View(result.Data);
        }

        public IActionResult Create()
        {
            return View(new EmploymentTypeDto());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(EmploymentTypeDto employmentType)
        {
            if (!ModelState.IsValid)
            {
                return View(employmentType);
            }

            var result = await _employmentTypeService.CreateAsync(employmentType);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return View(employmentType);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var result = await _employmentTypeService.GetByIdAsync(id);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            
            return View(result.Data);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(EmploymentTypeDto employmentType)
        {
            if (ModelState.IsValid)
            {
                var result = await _employmentTypeService.UpdateAsync(employmentType);
                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }
            return View(employmentType);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _employmentTypeService.DeleteAsync(id);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            return RedirectToAction(nameof(Index));
        }
    }
} 