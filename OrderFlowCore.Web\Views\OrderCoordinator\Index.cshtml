@* @model OrderFlowCore.Web.ViewModels.OrderCoordinatorViewModel
@{
    ViewBag.Title = "تحويل لاعتماد";
}

<h2 class="form-header">تحويل لاعتماد</h2>

@if (!string.IsNullOrEmpty(Model.SuccessMessage))
{
    <div class="alert alert-success">@Model.SuccessMessage</div>
}
@if (!string.IsNullOrEmpty(Model.ErrorMessage))
{
    <div class="alert alert-danger">@Model.ErrorMessage</div>
}

<!-- Order selection form (GET) -->
@using (Html.BeginForm("Index", "OrderCoordinator", FormMethod.Get, new { @class = "form-inline mb-3" }))
{
    <div class="dropdown-container">
        @Html.DropDownListFor(m => m.SelectedOrderNumber, new SelectList(Model.Orders, "Value", "Text", Model.SelectedOrderNumber), "-- اختر رقم الطلب --", new { @class = "custom-dropdown", style = "width:209px; font-size:20px;", onchange = "this.form.submit();" })
    </div>
}

<!-- Main workflow form (POST) -->
@using (Html.BeginForm("ProcessOrderAction", "OrderCoordinator", FormMethod.Post, new { @class = "form-container" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(m => m.SelectedOrderNumber)
    <div class="btn-group mb-3">
        <!-- TODO: Path buttons -->
        <button type="button" class="btn btn-primary">مسار1</button>
        <button type="button" class="btn btn-primary">مسار2</button>
        <button type="button" class="btn btn-primary">مسار3</button>
        <button type="button" class="btn btn-primary">مسار4</button>
        <button type="button" class="btn btn-primary">مسار5</button>
        <button type="button" class="btn btn-primary">مسار6</button>
    </div>
    <div class="supervisors-container">
        @for (int i = 0; i < Model.Supervisors.Count; i++)
        {
            <div class="checkbox-container">
                <input type="checkbox" name="CheckedSupervisors" value="@Model.Supervisors[i].DisplayName" id="<EMAIL>[i].Name" @(Model.CheckedSupervisors.Contains(Model.Supervisors[i].DisplayName) ? "checked" : "") />
                <label for="<EMAIL>[i].Name">@Model.Supervisors[i].DisplayName</label>
            </div>
        }
    </div>
    <div class="mb-3">
        @Html.TextBoxFor(m => m.DetailsNumber, new { @class = "form-control", placeholder = "التفاصيل والرقم" })
    </div>
    <div class="mb-3">
        @Html.TextBoxFor(m => m.ActionRequired, new { @class = "form-control", placeholder = "الإجراءات المطلوبة" })
    </div>
    <div class="mb-3">
        @Html.TextBoxFor(m => m.RejectReason, new { @class = "form-control", placeholder = "سبب الإلغاء/الإعادة" })
    </div>
    <div class="action-buttons mb-3">
        <button type="submit" class="btn btn-success" name="action" value="Submit">تحويل الطلب</button>
        <button type="submit" class="btn btn-info" name="action" value="NeedsAction">يتطلب إجراءات</button>
        <button type="submit" class="btn btn-warning" name="action" value="ReturnOrder">إعادة الطلب</button>
        <button type="submit" class="btn btn-danger" name="action" value="Reject">إلغاء الطلب</button>
        <button type="submit" class="btn btn-primary" name="action" value="AutoPath">التوجيه التلقائي</button>
        <button type="submit" class="btn btn-secondary" name="action" value="DirectToManager">تحويل للمدير</button>
    </div>
    <!-- Order Details Section -->
    @if (Model.ShowOrderDetails && Model.OrderDetails != null)
    {
        <div class="full-table-container">
            <table class="details-table">
                <tr>
                    <th>رقم الطلب</th>
                    <td>@Model.OrderDetails.OrderNumber</td>
                    <th>تاريخ الطلب</th>
                    <td>@Model.OrderDetails.OrderDate</td>
                </tr>
                <tr>
                    <th>حالة الطلب</th>
                    <td colspan="3">@Model.OrderDetails.OrderStatus</td>
                </tr>
            </table>
            <table class="details-table">
                <tr>
                    <th>نوع الطلب</th>
                    <td colspan="3">@Model.OrderDetails.OrderType</td>
                </tr>
                <tr>
                    <th>اسم الموظف</th>
                    <td>@Model.OrderDetails.EmployeeName</td>
                    <th>القسم</th>
                    <td>@Model.OrderDetails.Department</td>
                </tr>
                <tr>
                    <th>الوظيفة</th>
                    <td>@Model.OrderDetails.JobTitle</td>
                </tr>
                <tr>
                    <th>نوع التوظيف</th>
                    <td>@Model.OrderDetails.EmploymentType</td>
                    <th>المؤهل</th>
                    <td>@Model.OrderDetails.Qualification</td>
                </tr>
                <tr>
                    <th>رقم الموظف</th>
                    <td>@Model.OrderDetails.EmployeeNumber</td>
                    <th>السجل المدني</th>
                    <td>@Model.OrderDetails.CivilRegistry</td>
                </tr>
                <tr>
                    <th>الجنسية</th>
                    <td>@Model.OrderDetails.Nationality</td>
                    <th>رقم الجوال</th>
                    <td>@Model.OrderDetails.MobileNumber</td>
                </tr>
                <tr>
                    <td colspan="4">
                        <strong>تفاصيل مقدم الطلب:</strong><br />
                        @Model.OrderDetails.Notes
                    </td>
                </tr>
            </table>
            <table class="details-table">
                <tr>
                    <th>تم التأكيد/الإلغاء من مدير القسم</th>
                    <th>تم التأكيد/الإلغاء من قبل مساعد المدير</th>
                    <th>تم التحويل/الإلغاء من قبل المنسق</th>
                </tr>
                <tr>
                    <td>@Model.OrderDetails.ManagerApproval</td>
                    <td>@Model.OrderDetails.SupervisorApproval</td>
                    <td>@Model.OrderDetails.CoordinatorApproval</td>
                </tr>
                <tr>
                    <th>سبب الإلغاء/الإعادة</th>
                    <th colspan="2">تفاصيل المنسق</th>
                </tr>
                <tr>
                    <td>@Model.OrderDetails.CancellationReason</td>
                    <td colspan="2">@Model.OrderDetails.CoordinatorDetails</td>
                </tr>
            </table>
            <!-- Supervisor statuses table -->
            <table class="details-table">
                <tr>
                    <th>المشرف</th>
                    <th>الحالة</th>
                </tr>
                @foreach (var sup in Model.OrderDetails.SupervisorStatuses)
                {
                    <tr>
                        <td>@sup.Key</td>
                        <td>@sup.Value</td>
                    </tr>
                }
            </table>
            <table class="details-table">
                <tr>
                    <th>مدير الموارد البشرية</th>
                    <td colspan="3">@Model.OrderDetails.HRManagerApproval</td>
                </tr>
            </table>
        </div>
    }
}

<!-- Restore Section -->
<div class="admin-controls mt-5">
    <h4>إدارة الطلبات المحولة للمشرفين</h4>
    @using (Html.BeginForm("Index", "OrderCoordinator", FormMethod.Get, new { @class = "form-inline mb-3" }))
    {
        <div class="form-floating position-relative mb-3">
            @Html.DropDownListFor(m => m.SelectedRestorableOrderNumber, new SelectList(Model.RestorableOrders, "Value", "Text", Model.SelectedRestorableOrderNumber), "-- اختر رقم الطلب لاستعادته --", new { @class = "form-select form-select-lg border-2 border-primary rounded-3 shadow-sm", onchange = "this.form.submit();" })
            <label class="form-label text-primary fw-medium ps-4">اختر الطلب المراد استعادته</label>
        </div>
    }
    @if (Model.ShowRestoreDetails && Model.RestoreDetails != null)
    {
        <div class="alert alert-info mt-3">
            <div><strong>الحالة الحالية:</strong> @Model.RestoreDetails.CurrentStatus</div>
            <div><strong>تاريخ التحويل:</strong> @Model.RestoreDetails.TransferDate</div>
            <div><strong>المشرفون المحول لهم:</strong> @Model.RestoreDetails.AssignedSupervisors</div>
        </div>
        @using (Html.BeginForm("RestoreOrder", "OrderCoordinator", FormMethod.Post, new { @class = "mt-3" }))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.SelectedRestorableOrderNumber)
            <div class="form-floating mb-3">
                @Html.TextAreaFor(m => m.RestoreNotes, 5, 60, new { @class = "form-control border-2 border-primary rounded-3 shadow-sm", placeholder = "أدخل ملاحظاتك هنا..." })
                <label class="form-label text-primary fw-medium ps-4">ملاحظات الاستعادة (اختياري)</label>
            </div>
            <button type="submit" class="btn btn-lg btn-primary rounded-pill px-5 py-3 fw-bold shadow-lg">استعادة الطلب</button>
        }
    }
    @if (!string.IsNullOrEmpty(Model.RestoreSuccessMessage))
    {
        <div class="alert alert-success mt-3">@Model.RestoreSuccessMessage</div>
    }
    @if (!string.IsNullOrEmpty(Model.RestoreErrorMessage))
    {
        <div class="alert alert-danger mt-3">@Model.RestoreErrorMessage</div>
    }
</div>

<!-- TODO: Add restore section, scripts, and more UI as needed -->  *@