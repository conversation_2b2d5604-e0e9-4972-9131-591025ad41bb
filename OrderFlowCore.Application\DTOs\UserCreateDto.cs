using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Application.DTOs
{
    public class UserCreateDto
    {
        public string Username { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string Password { get; set; } = string.Empty;
        public string Permission { get; set; } = string.Empty;
    }
} 