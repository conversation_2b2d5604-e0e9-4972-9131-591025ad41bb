@model OrderFlowCore.Application.DTOs.AccountsDto
@{
    ViewData["Title"] = "إدارة الحسابات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">إدارة الحسابات</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active" aria-current="page">إدارة الحسابات</li>
                        </ol>
                    </nav>
                </div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                </button>
            </div>
        </div>
    </div>

    <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">إدارة المستخدمين</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <form asp-action="SearchUsers" method="post" class="d-flex">
                                        <input type="text" name="searchTerm" class="form-control me-2" placeholder="البحث عن مستخدم..." value="@Model.UserSearchTerm">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                            @if (Model.Users.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>اسم المستخدم</th>
                                                <th>القسم</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>الهاتف</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        @foreach (var user in Model.Users)
                                        {
                                            <tr>
                                                <td><strong>@user.Username</strong></td>
                                                <td>@user.Role</td>
                                                <td>@(user.Email ?? "-")</td>
                                                <td>@(user.Phone ?? "-")</td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editUser('@user.Username', '@user.Email', '@user.Phone')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmDeleteUser('@user.Username')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-5">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد حسابات</h5>
                                    <p class="text-muted">قم بإضافة مستخدم جديد للبدء</p>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                        <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                                    </button>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
</div>

<!-- Modal for adding user -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-action="CreateUser" method="post">
                <div class="modal-body">
                    @Html.AntiForgeryToken()
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم *</label>
                        <input type="text" class="form-control" id="username" name="Username" value="@Model.AddModel.Username" required />
                        <span asp-validation-for="AddModel.Username" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني (اختياري)</label>
                        <input type="email" class="form-control" id="email" name="Email" value="@Model.AddModel.Email" />
                        <span asp-validation-for="AddModel.Email" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">الهاتف (اختياري)</label>
                        <input type="tel" class="form-control" id="phone" name="Phone" value="@Model.AddModel.Phone" />
                        <span asp-validation-for="AddModel.Phone" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور *</label>
                        <input type="password" class="form-control" id="password" name="Password" minlength="6" required />
                        <span asp-validation-for="AddModel.Password" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label for="permission" class="form-label">القسم *</label>
                        <select class="form-control" id="permission" name="Permission" required>
                            <option value="">اختر القسم</option>
                            @foreach (var permission in Model.AvailablePermissions)
                            {
                                <option value="@permission" selected="@(Model.AddModel.Permission == permission ? true : false)">@permission</option>
                            }
                        </select>
                        <span asp-validation-for="AddModel.Permission" class="text-danger"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for editing user -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">تعديل بيانات المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-action="UpdateUser" method="post">
                <div class="modal-body">
                    @Html.AntiForgeryToken()
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="editUsername" name="Username" value="@Model.EditModel.Username" readonly />
                        <span asp-validation-for="EditModel.Username" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="editEmail" name="Email" value="@Model.EditModel.Email" />
                        <span asp-validation-for="EditModel.Email" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label for="editPhone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="editPhone" name="Phone" value="@Model.EditModel.Phone" />
                        <span asp-validation-for="EditModel.Phone" class="text-danger"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستخدم: <strong id="deleteUsername"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteUserForm" asp-action="DeleteUser" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="deleteUsernameInput" name="username" />
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="~/Views/Shared/_ValidationScriptsPartial.cshtml" />
    <script>
        function editUser(username, email, phone) {
            document.getElementById('editUsername').value = username;
            document.getElementById('editEmail').value = email;
            document.getElementById('editPhone').value = phone;
            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        }
        function confirmDeleteUser(username) {
            document.getElementById('deleteUsername').textContent = username;
            document.getElementById('deleteUsernameInput').value = username;
            new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
        }
        // Toastr notifications
        @if (TempData["SuccessMessage"] != null)
        {
            <text>toastr.success('@TempData["SuccessMessage"]');</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>toastr.error('@TempData["ErrorMessage"]');</text>
        }
    </script>
}
