using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Web.ViewModels;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class EmployeeController : Controller
    {
        private readonly IEmployeeService _employeeService;
        private readonly IDepartmentService _departmentService;
        private readonly IJobTypeService _jobTypeService;
        private readonly INationalityService _nationalityService;
        private readonly IEmploymentTypeService _employmentTypeService;
        private readonly IQualificationService _qualificationService;
        private readonly ILogger<EmployeeController> _logger;

        public EmployeeController(
            IEmployeeService employeeService,
            IDepartmentService departmentService,
            IJobTypeService jobTypeService,
            INationalityService nationalityService,
            IEmploymentTypeService employmentTypeService,
            IQualificationService qualificationService,
            ILogger<EmployeeController> logger)
        {
            _employeeService = employeeService;
            _departmentService = departmentService;
            _jobTypeService = jobTypeService;
            _nationalityService = nationalityService;
            _employmentTypeService = employmentTypeService;
            _qualificationService = qualificationService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var result = await _employeeService.GetAllAsync();
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Home");
            }
            
            return View(result.Data);
        }

        public async Task<IActionResult> Create()
        {
            var viewModel = new EmployeeViewModel();
            await PopulateDropdowns(viewModel);
            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(EmployeeViewModel viewModel)
        {
            if (!ModelState.IsValid)
            {
                await PopulateDropdowns(viewModel);
                return View(viewModel);
            }

            var dto = new EmployeeDto
            {
                Name = viewModel.Name,
                Job = viewModel.Job,
                EmployeeNumber = viewModel.EmployeeNumber,
                CivilNumber = viewModel.CivilNumber,
                Nationality = viewModel.Nationality,
                Mobile = viewModel.Mobile,
                EmploymentType = viewModel.EmploymentType,
                Qualification = viewModel.Qualification
            };

            var result = await _employeeService.CreateAsync(dto);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
                await PopulateDropdowns(viewModel);
            }

            return View(viewModel);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var result = await _employeeService.GetByIdAsync(id);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }

            var viewModel = new EmployeeViewModel
            {
                Id = result.Data.Id,
                Name = result.Data.Name,
                Job = result.Data.Job,
                EmployeeNumber = result.Data.EmployeeNumber,
                CivilNumber = result.Data.CivilNumber,
                Nationality = result.Data.Nationality,
                Mobile = result.Data.Mobile,
                EmploymentType = result.Data.EmploymentType,
                Qualification = result.Data.Qualification
            };

            await PopulateDropdowns(viewModel);
            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(EmployeeViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                var dto = new EmployeeDto
                {
                    Id = viewModel.Id,
                    Name = viewModel.Name,
                    Job = viewModel.Job,
                    EmployeeNumber = viewModel.EmployeeNumber,
                    CivilNumber = viewModel.CivilNumber,
                    Nationality = viewModel.Nationality,
                    Mobile = viewModel.Mobile,
                    EmploymentType = viewModel.EmploymentType,
                    Qualification = viewModel.Qualification
                };

                var result = await _employeeService.UpdateAsync(dto);
                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }

            await PopulateDropdowns(viewModel);
            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _employeeService.DeleteAsync(id);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            return RedirectToAction(nameof(Index));
        }

        private async Task PopulateDropdowns(EmployeeViewModel viewModel)
        {
            // Populate departments
            var departmentsResult = await _departmentService.GetAllDepartmentsAsync();
            if (departmentsResult.IsSuccess)
            {
                viewModel.Departments = departmentsResult.Data
                    .Select(d => new SelectListItem { Value = d.Name, Text = d.Name })
                    .ToList();
            }

            // Populate job types
            var jobTypesResult = await _jobTypeService.GetAllAsync();
            if (jobTypesResult.IsSuccess)
            {
                viewModel.JobTypes = jobTypesResult.Data
                    .Select(j => new SelectListItem { Value = j.Name, Text = j.Name })
                    .ToList();
            }

            // Populate nationalities
            var nationalitiesResult = await _nationalityService.GetAllAsync();
            if (nationalitiesResult.IsSuccess)
            {
                viewModel.Nationalities = nationalitiesResult.Data
                    .Select(n => new SelectListItem { Value = n.Name, Text = n.Name })
                    .ToList();
            }

            // Populate employment types
            var employmentTypesResult = await _employmentTypeService.GetAllAsync();
            if (employmentTypesResult.IsSuccess)
            {
                viewModel.EmploymentTypes = employmentTypesResult.Data
                    .Select(e => new SelectListItem { Value = e.Name, Text = e.Name })
                    .ToList();
            }

            // Populate qualifications
            var qualificationsResult = await _qualificationService.GetAllAsync();
            if (qualificationsResult.IsSuccess)
            {
                viewModel.Qualifications = qualificationsResult.Data
                    .Select(q => new SelectListItem { Value = q.Name, Text = q.Name })
                    .ToList();
            }
        }
    }
} 