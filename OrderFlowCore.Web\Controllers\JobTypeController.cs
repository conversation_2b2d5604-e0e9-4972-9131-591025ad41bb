using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;

using Microsoft.Extensions.Logging;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class JobTypeController : Controller
    {
        private readonly IJobTypeService _jobTypeService;
        
        private readonly ILogger<JobTypeController> _logger;

        public JobTypeController(IJobTypeService jobTypeService, ILogger<JobTypeController> logger)
        {
            _jobTypeService = jobTypeService;
            
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var result = await _jobTypeService.GetAllAsync();
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Home");
            }
            
            return View(result.Data);
        }

        public IActionResult Create()
        {
            return View(new JobTypeDto());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(JobTypeDto jobType)
        {
            if (!ModelState.IsValid)
            {
                return View(jobType);
            }

            var result = await _jobTypeService.CreateAsync(jobType);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return View(jobType);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var result = await _jobTypeService.GetByIdAsync(id);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            
            return View(result.Data);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(JobTypeDto jobType)
        {
            if (ModelState.IsValid)
            {
                var result = await _jobTypeService.UpdateAsync(jobType);
                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }
            return View(jobType);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _jobTypeService.DeleteAsync(id);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            return RedirectToAction(nameof(Index));
        }
    }
} 