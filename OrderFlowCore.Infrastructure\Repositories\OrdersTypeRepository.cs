using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Infrastructure;

namespace OrderFlowCore.Infrastructure.Data
{
    public class OrdersTypeRepository : IOrdersTypeRepository
    {
        private readonly ApplicationDbContext _context;

        public OrdersTypeRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<OrdersTypeDto>> GetAllAsync()
        {
            var ordersTypes = await _context.OrdersTypes
                .OrderBy(ot => ot.Name)
                .ToListAsync();

            return ordersTypes.Select(MapToDto);
        }

        public async Task<OrdersTypeDto?> GetByIdAsync(int id)
        {
            var ordersType = await _context.OrdersTypes
                .FirstOrDefaultAsync(ot => ot.Id == id);

            return ordersType != null ? MapToDto(ordersType) : null;
        }

        public async Task<bool> CreateAsync(OrdersTypeDto ordersTypeDto)
        {
            var ordersType = MapToEntity(ordersTypeDto);
            await _context.OrdersTypes.AddAsync(ordersType);
            
            return true;
        }

        public async Task<bool> UpdateAsync(OrdersTypeDto ordersTypeDto)
        {
            var ordersType = await _context.OrdersTypes.FindAsync(ordersTypeDto.Id);
            if (ordersType == null)
                return false;

            ordersType.Name = ordersTypeDto.Name;
            
            
            return true;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var ordersType = await _context.OrdersTypes.FindAsync(id);
            if (ordersType == null)
                return false;

            _context.OrdersTypes.Remove(ordersType);
            
            return true;
        }

        private static OrdersTypeDto MapToDto(OrdersType ordersType)
        {
            return new OrdersTypeDto
            {
                Id = ordersType.Id,
                Name = ordersType.Name,
                
            };
        }

        private static OrdersType MapToEntity(OrdersTypeDto ordersTypeDto)
        {
            return new OrdersType
            {
                Id = ordersTypeDto.Id,
                Name = ordersTypeDto.Name,
                
            };
        }
    }
} 