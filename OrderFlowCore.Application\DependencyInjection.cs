using Microsoft.Extensions.DependencyInjection;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Services;

namespace OrderFlowCore.Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            // Register application services here (if any)
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IDepartmentService, DepartmentService>();
            services.AddScoped<IJobTypeService, JobTypeService>();
            services.AddScoped<IEmploymentTypeService, EmploymentTypeService>();
            services.AddScoped<IQualificationService, QualificationService>();
            services.AddScoped<IOrdersTypeService, OrdersTypeService>();
            services.AddScoped<INationalityService, NationalityService>();
            services.AddScoped<IOrderService, OrderService>();

            // Register new thin controller services
            services.AddScoped<IDistributionService, DistributionService>();
            services.AddScoped<IAccountManagementService, AccountManagementService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            //services.AddScoped<ISupervisorsFollowUpService, SupervisorsFollowUpService>();
            //services.AddScoped<IPathsService, PathsService>();
            services.AddScoped<ICachedDropdownService, CachedDropdownService>();
            services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();
            services.AddScoped<IDashboardService, DashboardService>();

            return services;
        }
    }
} 