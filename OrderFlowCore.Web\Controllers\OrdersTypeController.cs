using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;

using Microsoft.Extensions.Logging;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class OrdersTypeController : Controller
    {
        private readonly IOrdersTypeService _ordersTypeService;
        
        private readonly ILogger<OrdersTypeController> _logger;

        public OrdersTypeController(IOrdersTypeService ordersTypeService, ILogger<OrdersTypeController> logger)
        {
            _ordersTypeService = ordersTypeService;
            
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var result = await _ordersTypeService.GetAllAsync();
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Home");
            }
            
            return View(result.Data);
        }

        public IActionResult Create()
        {
            return View(new OrdersTypeDto());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(OrdersTypeDto ordersType)
        {
            if (!ModelState.IsValid)
            {
                return View(ordersType);
            }

            var result = await _ordersTypeService.CreateAsync(ordersType);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return View(ordersType);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var result = await _ordersTypeService.GetByIdAsync(id);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            
            return View(result.Data);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(OrdersTypeDto ordersType)
        {
            if (ModelState.IsValid)
            {
                var result = await _ordersTypeService.UpdateAsync(ordersType);
                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }
            return View(ordersType);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _ordersTypeService.DeleteAsync(id);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            return RedirectToAction(nameof(Index));
        }
    }
} 