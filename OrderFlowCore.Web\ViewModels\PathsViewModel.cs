using System.Collections.Generic;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Web.ViewModels
{
    public class PathsViewModel
    {
        // Add properties as needed for manual, direct, auto routing, settings, employees, etc.
        // Example:
        // public List<ManualRoute> ManualRoutes { get; set; }
        // public List<DirectRoute> DirectRoutes { get; set; }
        // public List<AutoRoute> AutoRoutes { get; set; }
        // public SettingsViewModel Settings { get; set; }
        // public List<Employee> Employees { get; set; }
        public List<ManualPathDto> ManualPaths { get; set; }
        public List<DirectRouteDto> DirectRoutes { get; set; }
        public DirectRouteDto NewDirectRoute { get; set; }
        public List<AutoRouteDto> AutoRoutes { get; set; }
        public AutoRouteDto NewAutoRoute { get; set; }
        public SettingsDto Settings { get; set; }
        public EmployeeImportResultDto EmployeeImportResult { get; set; }
        public int EmployeeCount { get; set; }
    }
} 