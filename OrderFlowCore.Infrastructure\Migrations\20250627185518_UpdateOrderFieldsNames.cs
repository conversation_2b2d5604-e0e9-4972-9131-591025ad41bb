﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OrderFlowCore.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateOrderFieldsNames : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ConfirmedBySupervisor",
                table: "ordersTable",
                newName: "ConfirmedByCoordinator");

            migrationBuilder.RenameColumn(
                name: "CancelledByCoordinator",
                table: "ordersTable",
                newName: "ConfirmedByAssistantManager");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ConfirmedByCoordinator",
                table: "ordersTable",
                newName: "ConfirmedBySupervisor");

            migrationBuilder.RenameColumn(
                name: "ConfirmedByAssistantManager",
                table: "ordersTable",
                newName: "CancelledByCoordinator");
        }
    }
}
