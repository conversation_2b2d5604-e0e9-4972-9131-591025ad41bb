//using Microsoft.AspNetCore.Mvc;
//using OrderFlowCore.Web.ViewModels;
//using OrderFlowCore.Application.Interfaces.Services;
//using Microsoft.AspNetCore.Http;
//using System.Text;

//namespace OrderFlowCore.Web.Controllers
//{
//    public class PathsController : Controller
//    {
//        private readonly IPathsService _pathsService;
//        public PathsController(IPathsService pathsService)
//        {
//            _pathsService = pathsService;
//        }
//        // GET: /Paths/
//        public async Task<IActionResult> Index()
//        {
//            var model = new PathsViewModel
//            {
//                ManualPaths = await _pathsService.GetManualPathsAsync(),
//                DirectRoutes = await _pathsService.GetDirectRoutesAsync(),
//                NewDirectRoute = new DirectRouteDto(),
//                AutoRoutes = await _pathsService.GetAutoRoutesAsync(),
//                NewAutoRoute = new AutoRouteDto(),
//                Settings = await _pathsService.GetSettingsAsync(),
//                EmployeeCount = await _pathsService.GetEmployeeCountAsync(),
//                EmployeeImportResult = TempData["EmployeeImportResult"] as EmployeeImportResultDto
//            };
//            return View(model);
//        }

//        [HttpPost]
//        public async Task<IActionResult> UpdateManualPath(string pathName, List<string> departments)
//        {
//            await _pathsService.UpdateManualPathAsync(pathName, departments);
//            return RedirectToAction("Index");
//        }

//        [HttpPost]
//        public async Task<IActionResult> AddOrUpdateDirectRoute(DirectRouteDto dto)
//        {
//            await _pathsService.AddOrUpdateDirectRouteAsync(dto);
//            return RedirectToAction("Index");
//        }

//        [HttpPost]
//        public async Task<IActionResult> DeleteDirectRoute(int id)
//        {
//            await _pathsService.DeleteDirectRouteAsync(id);
//            return RedirectToAction("Index");
//        }

//        [HttpPost]
//        public async Task<IActionResult> AddOrUpdateAutoRoute(AutoRouteDto dto)
//        {
//            await _pathsService.AddOrUpdateAutoRouteAsync(dto);
//            return RedirectToAction("Index");
//        }

//        [HttpPost]
//        public async Task<IActionResult> DeleteAutoRoute(int id)
//        {
//            await _pathsService.DeleteAutoRouteAsync(id);
//            return RedirectToAction("Index");
//        }

//        [HttpPost]
//        public async Task<IActionResult> ImportEmployees(IFormFile file)
//        {
//            var result = await _pathsService.ImportEmployeesAsync(file);
//            TempData["EmployeeImportResult"] = result;
//            return RedirectToAction("Index");
//        }

//        [HttpPost]
//        public async Task<IActionResult> ExportEmployees()
//        {
//            var csv = await _pathsService.ExportEmployeesAsync();
//            var bytes = Encoding.UTF8.GetBytes(csv);
//            return File(bytes, "text/csv", "Employees.csv");
//        }

//        [HttpPost]
//        public async Task<IActionResult> DeleteAllEmployees()
//        {
//            await _pathsService.DeleteAllEmployeesAsync();
//            return RedirectToAction("Index");
//        }
//    }
//} 