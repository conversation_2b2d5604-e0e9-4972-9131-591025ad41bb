using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;

using Microsoft.Extensions.Logging;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class QualificationController : Controller
    {
        private readonly IQualificationService _qualificationService;
        
        private readonly ILogger<QualificationController> _logger;

        public QualificationController(IQualificationService qualificationService, ILogger<QualificationController> logger)
        {
            _qualificationService = qualificationService;
            
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var result = await _qualificationService.GetAllAsync();
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Home");
            }
            
            return View(result.Data);
        }

        public IActionResult Create()
        {
            return View(new QualificationDto());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(QualificationDto qualification)
        {
            if (!ModelState.IsValid)
            {
                return View(qualification);
            }

            var result = await _qualificationService.CreateAsync(qualification);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return View(qualification);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var result = await _qualificationService.GetByIdAsync(id);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            
            return View(result.Data);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(QualificationDto qualification)
        {
            if (ModelState.IsValid)
            {
                var result = await _qualificationService.UpdateAsync(qualification);
                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }
            return View(qualification);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _qualificationService.DeleteAsync(id);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            return RedirectToAction(nameof(Index));
        }
    }
} 