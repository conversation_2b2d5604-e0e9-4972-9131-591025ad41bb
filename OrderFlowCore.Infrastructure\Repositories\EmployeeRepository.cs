using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Infrastructure.Data
{
    public class EmployeeRepository : IEmployeeRepository
    {
        private readonly ApplicationDbContext _context;

        public EmployeeRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<EmployeeDto?> GetByCivilNumberAsync(string civilNumber)
        {
            var employee = await _context.Employees
                .FirstOrDefaultAsync(e => e.CivilNumber == civilNumber);

            return employee != null ? MapToDto(employee) : null;
        }

        public async Task<IEnumerable<EmployeeDto>> GetAllAsync()
        {
            var employees = await _context.Employees
                .OrderBy(e => e.Name)
                .ToListAsync();

            return employees.Select(MapToDto);
        }

        public async Task<EmployeeDto?> GetByIdAsync(int id)
        {
            var employee = await _context.Employees
                .FirstOrDefaultAsync(e => e.Id == id);

            return employee != null ? MapToDto(employee) : null;
        }

        public async Task<bool> CreateAsync(EmployeeDto employeeDto)
        {
            var employee = MapToEntity(employeeDto);
            await _context.Employees.AddAsync(employee);
            
            return true;
        }

        public async Task<bool> UpdateAsync(EmployeeDto employeeDto)
        {
            var employee = await _context.Employees.FindAsync(employeeDto.Id);
            if (employee == null)
                return false;

            employee.Name = employeeDto.Name;
            employee.Job = employeeDto.Job;
            employee.EmployeeNumber = employeeDto.EmployeeNumber;
            employee.CivilNumber = employeeDto.CivilNumber;
            employee.Nationality = employeeDto.Nationality;
            employee.Mobile = employeeDto.Mobile;
            employee.EmploymentType = employeeDto.EmploymentType;
            employee.Qualification = employeeDto.Qualification;
            
            return true;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var employee = await _context.Employees.FindAsync(id);
            if (employee == null)
                return false;

            _context.Employees.Remove(employee);
            
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Employees.AnyAsync(e => e.Id == id);
        }

        private static EmployeeDto MapToDto(Employee employee)
        {
            return new EmployeeDto
            {
                Id = employee.Id,
                Name = employee.Name,
                Job = employee.Job,
                EmployeeNumber = employee.EmployeeNumber,
                CivilNumber = employee.CivilNumber,
                Nationality = employee.Nationality,
                Mobile = employee.Mobile,
                EmploymentType = employee.EmploymentType,
                Qualification = employee.Qualification
            };
        }

        private static Employee MapToEntity(EmployeeDto employeeDto)
        {
            return new Employee
            {
                Id = employeeDto.Id,
                Name = employeeDto.Name,
                Job = employeeDto.Job,
                EmployeeNumber = employeeDto.EmployeeNumber,
                CivilNumber = employeeDto.CivilNumber,
                Nationality = employeeDto.Nationality,
                Mobile = employeeDto.Mobile,
                EmploymentType = employeeDto.EmploymentType,
                Qualification = employeeDto.Qualification
            };
        }
    }
} 