using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.ViewModels
{
    public class OrderPrintDetailsViewModel
    {
        public string OrderNumber { get; set; }
        public string OrderDate { get; set; }
        public string OrderStatus { get; set; }
        public string OrderType { get; set; }
        public string EmployeeName { get; set; }
        public string Department { get; set; }
        public string Notes { get; set; }
        public string JobTitle { get; set; }
        public string EmployeeNumber { get; set; }
        public string CivilRegistry { get; set; }
        public string Nationality { get; set; }
        public string MobileNumber { get; set; }
        public string EmploymentType { get; set; }
        public string Qualification { get; set; }
        public string ManagerApproval { get; set; }
        public string SupervisorApproval { get; set; }
        public string CoordinatorApproval { get; set; }
        public string CancellationReason { get; set; }
        public string CoordinatorDetails { get; set; }
        public string HRManagerApproval { get; set; }
        // Add more fields as needed for all supervisor/approval columns
    }
} 