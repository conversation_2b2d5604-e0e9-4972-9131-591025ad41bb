using System.Collections.Generic;

namespace OrderFlowCore.Web.ViewModels
{
    public class SupervisorFollowUpViewModel
    {
        // Dropdown for order numbers
        public List<OrderDropdownItem> OrderNumbers { get; set; } = new List<OrderDropdownItem>();
        public string SelectedOrderNumber { get; set; }

        // Order details
        public OrderDetailsViewModel OrderDetails { get; set; }

        // Follow-up records
        public List<FollowUpRecordViewModel> FollowUpRecords { get; set; } = new List<FollowUpRecordViewModel>();

        // Form fields
        public string ActionRequired { get; set; }
        public string RejectReason { get; set; }
        public string CivilRegistry { get; set; }
        public string OwnerName { get; set; }
        public string SpecialProcedure { get; set; }

        // Messages
        public string Message { get; set; }
        public string Error { get; set; }
    }

    public class OrderDropdownItem
    {
        public string Value { get; set; }
        public string Text { get; set; }
    }

    public class FollowUpRecordViewModel
    {
        public string CivilRegistry { get; set; }
        public string OwnerName { get; set; }
        public string SpecialProcedure { get; set; }
    }
} 