@model OrderFlowCore.Application.DTOs.EmploymentTypeDto
@{
    ViewData["Title"] = "إضافة نوع توظيف جديد";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="container mt-4" dir="rtl">
    <h2 class="mb-4">إضافة نوع توظيف جديد</h2>
    <form asp-action="Create" method="post">
        <div class="form-group mb-3">
            <label asp-for="Name"></label>
            <input asp-for="Name" class="form-control" />
            <span asp-validation-for="Name" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-success">حفظ</button>
        <a asp-action="Index" class="btn btn-secondary">إلغاء</a>
    </form>
</div>
@section Scripts {
    <partial name="~/Views/Shared/_ValidationScriptsPartial.cshtml" />
    <script>
        $(function () {
            var error = '@TempData["ToastrError"]';
            if (error) toastr.error(error);
        });
    </script>
} 