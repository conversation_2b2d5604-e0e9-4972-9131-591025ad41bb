@model OrderFlowCore.Web.ViewModels.PathsViewModel

@{
    ViewData["Title"] = "إدارة المسارات";
}

<h2>إدارة المسارات اليدوية</h2>

@foreach (var path in Model.ManualPaths ?? new List<OrderFlowCore.Application.DTOs.ManualPathDto>())
{
    <div class="card mb-4">
        <div class="card-header">
            <h4>@path.PathName</h4>
        </div>
        <div class="card-body">
            <form asp-action="UpdateManualPath" method="post">
                <input type="hidden" name="pathName" value="@path.PathName" />
                <div class="checkbox-list">
                    @foreach (var dept in path.Departments)
                    {
                        <label>
                            <input type="checkbox" name="departments" value="@dept" checked />
                            @dept
                        </label>
                    }
                </div>
                <button type="submit" class="btn btn-primary mt-2">تحديث المسار</button>
            </form>
        </div>
    </div>
}

<h2>المسار السريع (Direct Routing)</h2>
<div class="card mb-4">
    <div class="card-header">
        <h4>إضافة/تعديل مسار سريع</h4>
    </div>
    <div class="card-body">
        <form asp-action="AddOrUpdateDirectRoute" method="post">
            <input type="hidden" asp-for="NewDirectRoute.Id" />
            <div class="form-group">
                <label>نوع الطلب:</label>
                <input asp-for="NewDirectRoute.RequestType" class="form-control" />
            </div>
            <div class="form-group">
                <label>الجنسية:</label>
                <input asp-for="NewDirectRoute.Nationality" class="form-control" />
            </div>
            <div class="form-group">
                <label>الوظيفة:</label>
                <input asp-for="NewDirectRoute.Job" class="form-control" />
            </div>
            <div class="form-group">
                <label>المشرفين (افصل بينهم بفاصلة منقوطة):</label>
                <input asp-for="NewDirectRoute.Supervisors" class="form-control" />
            </div>
            <div class="form-group">
                <label>ملاحظات:</label>
                <input asp-for="NewDirectRoute.Notes" class="form-control" />
            </div>
            <div class="form-group">
                <label>حالة المسار:</label>
                <input type="checkbox" asp-for="NewDirectRoute.Status" /> نشط
            </div>
            <button type="submit" class="btn btn-primary">حفظ</button>
        </form>
    </div>
</div>

<table class="table table-bordered table-hover">
    <thead>
        <tr>
            <th>نوع الطلب</th>
            <th>الجنسية</th>
            <th>الوظيفة</th>
            <th>المشرفين</th>
            <th>الحالة</th>
            <th>ملاحظات</th>
            <th>تاريخ الإنشاء</th>
            <th>العمليات</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var route in Model.DirectRoutes ?? new List<OrderFlowCore.Application.DTOs.DirectRouteDto>())
        {
            <tr>
                <td>@route.RequestType</td>
                <td>@route.Nationality</td>
                <td>@route.Job</td>
                <td>@string.Join("; ", route.Supervisors)</td>
                <td>@(route.Status ? "نشط" : "غير نشط")</td>
                <td>@route.Notes</td>
                <td>@route.CreatedAt.ToString("yyyy-MM-dd")</td>
                <td>
                    <!-- Edit button could be implemented with JS to fill the form above -->
                    <form asp-action="DeleteDirectRoute" method="post" style="display:inline;">
                        <input type="hidden" name="id" value="@route.Id" />
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</button>
                    </form>
                </td>
            </tr>
        }
    </tbody>
</table>

<h2>التوجيه التلقائي (Auto Routing)</h2>
<div class="card mb-4">
    <div class="card-header">
        <h4>إضافة/تعديل مسار تلقائي</h4>
    </div>
    <div class="card-body">
        <form asp-action="AddOrUpdateAutoRoute" method="post">
            <input type="hidden" asp-for="NewAutoRoute.Id" />
            <div class="form-group">
                <label>نوع الطلب:</label>
                <input asp-for="NewAutoRoute.RequestType" class="form-control" />
            </div>
            <div class="form-group">
                <label>الجنسية:</label>
                <input asp-for="NewAutoRoute.Nationality" class="form-control" />
            </div>
            <div class="form-group">
                <label>الوظيفة:</label>
                <input asp-for="NewAutoRoute.Job" class="form-control" />
            </div>
            <div class="form-group">
                <label>المشرفين (افصل بينهم بفاصلة منقوطة):</label>
                <input asp-for="NewAutoRoute.Supervisors" class="form-control" />
            </div>
            <div class="form-group">
                <label>ملاحظات:</label>
                <input asp-for="NewAutoRoute.Notes" class="form-control" />
            </div>
            <div class="form-group">
                <label>حالة المسار:</label>
                <input type="checkbox" asp-for="NewAutoRoute.Status" /> نشط
            </div>
            <button type="submit" class="btn btn-primary">حفظ</button>
        </form>
    </div>
</div>

<table class="table table-bordered table-hover">
    <thead>
        <tr>
            <th>نوع الطلب</th>
            <th>الجنسية</th>
            <th>الوظيفة</th>
            <th>المشرفين</th>
            <th>الحالة</th>
            <th>ملاحظات</th>
            <th>تاريخ الإنشاء</th>
            <th>العمليات</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var route in Model.AutoRoutes ?? new List<OrderFlowCore.Application.DTOs.AutoRouteDto>())
        {
            <tr>
                <td>@route.RequestType</td>
                <td>@route.Nationality</td>
                <td>@route.Job</td>
                <td>@string.Join("; ", route.Supervisors)</td>
                <td>@(route.Status ? "نشط" : "غير نشط")</td>
                <td>@route.Notes</td>
                <td>@route.CreatedAt.ToString("yyyy-MM-dd")</td>
                <td>
                    <!-- Edit button could be implemented with JS to fill the form above -->
                    <form asp-action="DeleteAutoRoute" method="post" style="display:inline;">
                        <input type="hidden" name="id" value="@route.Id" />
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</button>
                    </form>
                </td>
            </tr>
        }
    </tbody>
</table>

<h2>الإعدادات (Settings)</h2>
<div class="card mb-4">
    <div class="card-header">
        <h4>إعدادات النظام</h4>
    </div>
    <div class="card-body">
        <form asp-action="UpdateSettings" method="post">
            <div class="form-group">
                <label>الحد الأقصى للمسارات النشطة:</label>
                <input asp-for="Settings.MaxActivePaths" class="form-control" type="number" />
            </div>
            <div class="form-group">
                <label>الحد الأقصى للمشرفين في المسار:</label>
                <input asp-for="Settings.MaxSupervisors" class="form-control" type="number" />
            </div>
            <div class="form-group">
                <label>
                    <input asp-for="Settings.AutoDeactivate" type="checkbox" /> تعطيل المسارات القديمة تلقائياً
                </label>
            </div>
            <div class="form-group">
                <label>
                    <input asp-for="Settings.RequireNotes" type="checkbox" /> إلزامية إدخال الملاحظات
                </label>
            </div>
            <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
        </form>
    </div>
</div>

<h2>إدارة بيانات الموظفين (Employee Import/Export)</h2>
<div class="card mb-4">
    <div class="card-header">
        <h4>استيراد/تصدير بيانات الموظفين</h4>
    </div>
    <div class="card-body">
        <form asp-action="ImportEmployees" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label>استيراد ملف CSV:</label>
                <input type="file" name="file" accept=".csv" class="form-control" required />
            </div>
            <button type="submit" class="btn btn-primary">تحميل الملف</button>
        </form>
        <form asp-action="ExportEmployees" method="post" style="display:inline;">
            <button type="submit" class="btn btn-success mt-2">تصدير CSV</button>
        </form>
        <form asp-action="DeleteAllEmployees" method="post" style="display:inline;">
            <button type="submit" class="btn btn-danger mt-2" onclick="return confirm('هل أنت متأكد من حذف جميع البيانات؟');">حذف جميع البيانات</button>
        </form>
        <div class="mt-3">
            <strong>إجمالي عدد الموظفين:</strong> @Model.EmployeeCount
        </div>
        @if (Model.EmployeeImportResult != null)
        {
            <div class="alert @(Model.EmployeeImportResult.ErrorCount > 0 ? "alert-danger" : "alert-success") mt-3">
                <strong>نتيجة الاستيراد:</strong>
                <div>تم استيراد/تحديث @Model.EmployeeImportResult.SuccessCount سجل بنجاح.</div>
                @if (Model.EmployeeImportResult.ErrorCount > 0)
                {
                    <div>فشل @Model.EmployeeImportResult.ErrorCount سجل.</div>
                    <ul>
                        @foreach (var msg in Model.EmployeeImportResult.ErrorMessages)
                        {
                            <li>@msg</li>
                        }
                    </ul>
                }
            </div>
        }
    </div>
</div> 