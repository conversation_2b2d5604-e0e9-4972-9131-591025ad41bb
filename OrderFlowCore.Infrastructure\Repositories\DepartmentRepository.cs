using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Infrastructure.Data;

public class DepartmentRepository : IDepartmentRepository
{
    private readonly ApplicationDbContext _context;

    public DepartmentRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<DepartmentDto>> GetAllAsync()
    {
        var departments = await _context.Departments
            .OrderBy(d => d.Name)
            .ToListAsync();

        return departments.Select(MapToDto);
    }

    public async Task<DepartmentDto?> GetByIdAsync(int id)
    {
        var department = await _context.Departments
            .FirstOrDefaultAsync(d => d.Id == id);

        return department != null ? MapToDto(department) : null;
    }
    public async Task<DepartmentDto?> GetByNameAsync(string name)
    {
        var department = await _context.Departments
            .FirstOrDefaultAsync(d => d.Name == name);

        return department != null ? MapToDto(department) : null;
    }

    public async Task<DepartmentDto> CreateAsync(DepartmentDto departmentDto)
    {
        var department = MapToEntity(departmentDto);
        await _context.Departments.AddAsync(department);
        
        return MapToDto(department);
    }

    public async Task<DepartmentDto?> UpdateAsync(DepartmentDto departmentDto)
    {
        var department = await _context.Departments.FindAsync(departmentDto.Id);
        if (department == null)
            return null;

        department.Name = departmentDto.Name;
        department.Description = departmentDto.Description;
        department.IsActive = departmentDto.IsActive;
        department.AssistantManagerId = departmentDto.AssistantManagerId;

        return MapToDto(department);
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var department = await _context.Departments.FindAsync(id);
        if (department == null)
            return false;

        _context.Departments.Remove(department);
        
        return true;
    }

    public async Task<bool> ExistsAsync(string name, int? excludeId = null)
    {
        var query = _context.Departments.Where(d => d.Name == name);
        
        if (excludeId.HasValue)
        {
            query = query.Where(d => d.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    private static DepartmentDto MapToDto(Department department)
    {
        return new DepartmentDto
        {
            Id = department.Id,
            Name = department.Name,
            Description = department.Description,
            IsActive = department.IsActive,
            CreatedAt = department.CreatedAt,
            AssistantManagerId = department.AssistantManagerId
        };
    }

    private static Department MapToEntity(DepartmentDto departmentDto)
    {
        return new Department
        {
            Id = departmentDto.Id,
            Name = departmentDto.Name,
            Description = departmentDto.Description,
            IsActive = departmentDto.IsActive,
            CreatedAt = departmentDto.CreatedAt,
            AssistantManagerId = departmentDto.AssistantManagerId
        };
    }
} 