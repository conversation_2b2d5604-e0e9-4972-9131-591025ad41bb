//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;

//using OrderFlowCore.Web.ViewModels;
//using OrderFlowCore.Application.Services;
//using OrderFlowCore.Application.DTOs;
//using Microsoft.AspNetCore.Mvc;
//using OrderFlowCore.Application.Common;

//namespace OrderFlowCore.Web.Controllers
//{
//    public class OrderCoordinatorController : Controller
//    {
//        private readonly OrderService _orderService;
//        public OrderCoordinatorController(OrderService orderService)
//        {
//            _orderService = orderService;
//        }

//        // GET: OrderCoordinator
//        public async Task<ActionResult> Index(string selectedOrderNumber = null, string filter = "today", string searchTerm = "", string selectedRestorableOrderNumber = null)
//        {
//            var model = await BuildViewModel(selectedOrderNumber, filter, searchTerm, selectedRestorableOrderNumber);
//            return View(model);
//        }

//        [HttpPost]
//        [ValidateAntiForgeryToken]
//        public async Task<ActionResult> ProcessOrderAction(OrderCoordinatorViewModel model, string action)
//        {
//            string username = User.Identity.Name ?? "user";
//            ServiceResult result = null;
//            switch (action)
//            {
//                case "Submit":
//                    result = await _orderService.SubmitOrderAsync(model.SelectedOrderNumber, model.DetailsNumber, username, model.CheckedSupervisors);
//                    break;
//                case "NeedsAction":
//                    result = await _orderService.MarkOrderNeedsActionAsync(model.SelectedOrderNumber, model.ActionRequired, username);
//                    break;
//                case "ReturnOrder":
//                    result = await _orderService.ReturnOrderAsync(model.SelectedOrderNumber, model.RejectReason, username);
//                    break;
//                case "Reject":
//                    result = await _orderService.RejectOrderAsync(model.SelectedOrderNumber, model.RejectReason, username);
//                    break;
//                case "AutoPath":
//                    result = await _orderService.ApplyAutoPathAsync(model.SelectedOrderNumber, model.DetailsNumber, username);
//                    break;
//                case "DirectToManager":
//                    result = await _orderService.DirectToManagerAsync(model.SelectedOrderNumber, model.DetailsNumber, username);
//                    break;
//                default:
//                    model.ErrorMessage = "حدث خطأ: إجراء غير معروف.";
//                    break;
//            }
//            if (result != null)
//            {
//                if (result.Success)
//                {
//                    model.SuccessMessage = result.Message ?? "تمت العملية بنجاح.";
//                }
//                else
//                {
//                    model.ErrorMessage = result.ErrorMessage ?? "حدث خطأ أثناء تنفيذ العملية.";
//                }
//            }
//            // Reload model for view
//            var newModel = await BuildViewModel(model.SelectedOrderNumber, "today", "", model.SelectedRestorableOrderNumber);
//            newModel.SuccessMessage = model.SuccessMessage;
//            newModel.ErrorMessage = model.ErrorMessage;
//            return View("Index", newModel);
//        }

//        [HttpPost]
//        [ValidateAntiForgeryToken]
//        public async Task<ActionResult> RestoreOrder(OrderCoordinatorViewModel model)
//        {
//            if (string.IsNullOrEmpty(model.SelectedRestorableOrderNumber))
//            {
//                model.RestoreErrorMessage = "يرجى اختيار رقم الطلب.";
//            }
//            else
//            {
//                string coordinatorUsername = User.Identity.Name ?? "coordinator";
//                var result = await _orderService.RestoreOrderAsync(model.SelectedRestorableOrderNumber, model.RestoreNotes, coordinatorUsername);
//                if (result.IsSuccess)
//                {
//                    model.RestoreSuccessMessage = "تم استعادة الطلب بنجاح.";
//                }
//                else
//                {
//                    model.RestoreErrorMessage = result.ErrorMessage;
//                }
//            }
//            // Reload restore section
//            var newModel = await BuildViewModel(model.SelectedOrderNumber, "today", "", model.SelectedRestorableOrderNumber);
//            newModel.RestoreSuccessMessage = model.RestoreSuccessMessage;
//            newModel.RestoreErrorMessage = model.RestoreErrorMessage;
//            return View("Index", newModel);
//        }

//        // --- Private helpers ---
//        private async Task<OrderCoordinatorViewModel> BuildViewModel(string selectedOrderNumber, string filter, string searchTerm, string selectedRestorableOrderNumber)
//        {
//            var model = new OrderCoordinatorViewModel();
//            model.Orders = await GetOrderDropdownAsync();
//            model.SelectedOrderNumber = selectedOrderNumber;
//            model.Supervisors = GetSupervisorsList();
//            model.RestorableOrders = await GetRestorableOrderDropdownAsync(filter, searchTerm);
//            model.SelectedRestorableOrderNumber = selectedRestorableOrderNumber;

//            if (!string.IsNullOrEmpty(selectedOrderNumber))
//            {
//                model.OrderDetails = await GetOrderDetailsAsync(selectedOrderNumber);
//                model.ShowOrderDetails = model.OrderDetails != null;
//            }
//            if (!string.IsNullOrEmpty(selectedRestorableOrderNumber))
//            {
//                model.RestoreDetails = await GetRestoreDetailsAsync(selectedRestorableOrderNumber);
//                model.ShowRestoreDetails = model.RestoreDetails != null;
//            }
//            return model;
//        }

//        private async Task<List<OrderDropdownItem>> GetOrderDropdownAsync()
//        {
//            var orderDtos = await _orderService.GetPendingOrdersAsync();
//            return orderDtos.Select(o => new OrderDropdownItem { Value = o.OrderNumber, Text = o.DisplayText }).ToList();
//        }

//        private async Task<List<RestorableOrderDropdownItem>> GetRestorableOrderDropdownAsync(string filter, string searchTerm)
//        {
//            var restorableDtos = await _orderService.GetRestorableOrdersAsync(searchTerm, filter);
//            return restorableDtos.Select(r => new RestorableOrderDropdownItem { Value = r.OrderNumber, Text = r.DisplayText }).ToList();
//        }

//        private List<SupervisorCheckboxViewModel> GetSupervisorsList()
//        {
//            return new List<SupervisorCheckboxViewModel>
//            {
//                new SupervisorCheckboxViewModel { Name = "1", DisplayName = "خدمات الموظفين" },
//                new SupervisorCheckboxViewModel { Name = "2", DisplayName = "إدارة تخطيط الموارد البشرية" },
//                new SupervisorCheckboxViewModel { Name = "3", DisplayName = "إدارة تقنية المعلومات" },
//                new SupervisorCheckboxViewModel { Name = "4", DisplayName = "مراقبة الدوام" },
//                new SupervisorCheckboxViewModel { Name = "5", DisplayName = "السجلات الطبية" },
//                new SupervisorCheckboxViewModel { Name = "6", DisplayName = "إدارة الرواتب والاستحقاقات" },
//                new SupervisorCheckboxViewModel { Name = "7", DisplayName = "إدارة القانونية والالتزام" },
//                new SupervisorCheckboxViewModel { Name = "8", DisplayName = "خدمات الموارد البشرية" },
//                new SupervisorCheckboxViewModel { Name = "9", DisplayName = "إدارة الإسكان" },
//                new SupervisorCheckboxViewModel { Name = "10", DisplayName = "قسم الملفات" },
//                new SupervisorCheckboxViewModel { Name = "11", DisplayName = "العيادات الخارجية" },
//                new SupervisorCheckboxViewModel { Name = "12", DisplayName = "التأمينات الاجتماعية" },
//                new SupervisorCheckboxViewModel { Name = "13", DisplayName = "وحدة مراقبة المخزون" },
//                new SupervisorCheckboxViewModel { Name = "14", DisplayName = "إدارة تنمية الإيرادات" },
//                new SupervisorCheckboxViewModel { Name = "15", DisplayName = "إدارة الأمن و السلامة" },
//                new SupervisorCheckboxViewModel { Name = "16", DisplayName = "الطب الاتصالي" }
//            };
//        }

//        private async Task<OrderDetailsViewModel> GetOrderDetailsAsync(string orderNumber)
//        {
//            var detailsDto = await _orderService.GetOrderDetailsAsync(orderNumber);
//            if (detailsDto == null) return null;
//            return  OrderDetailsViewModel.FromDto(detailsDto);

//        }

//        private async Task<RestoreDetailsViewModel> GetRestoreDetailsAsync(string orderNumber)
//        {
//            var restoreDetailsDto = await _orderService.GetRestoreDetailsAsync(orderNumber);
//            if (restoreDetailsDto == null) return null;
//            return new RestoreDetailsViewModel
//            {
//                CurrentStatus = restoreDetailsDto.CurrentStatus,
//                TransferDate = restoreDetailsDto.TransferDate,
//                AssignedSupervisors = restoreDetailsDto.AssignedSupervisors
//            };
//        }
//    }
//} 