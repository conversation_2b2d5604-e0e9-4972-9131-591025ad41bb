using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Infrastructure.Data
{
    public class OrderRepository : IOrderRepository
    {
        private readonly ApplicationDbContext _context;
        public OrderRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<int> AddAsync(OrdersTable order)
        {
            _context.OrdersTables.Add(order);
            return order.Id;
        }
        public async Task<int> UpdateAsync(OrdersTable order)
        {
            _context.OrdersTables.Update(order);
            return order.Id;
        }

        public async Task<OrdersTable> GetByIdAsync(int id)
        {
            return await _context.OrdersTables.FindAsync(id);
        }

        public async Task<List<OrdersTable>> GetAllAsync()
        {
            return await _context.OrdersTables.ToListAsync();
        }

        public async Task<List<OrdersTable>> GetPendingOrdersForDirectMangerAsync()
        {
            // Get orders that are pending manager approval (ConfirmedByDepartmentManager is null or empty)
            return await _context.OrdersTables
                .Where(o => o.OrderStatus == OrderStatus.DM || o.OrderStatus == OrderStatus.ReturnedByAssistantManager)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId)
        {
            OrderStatus status = assistantManagerId.ToOrderStatus();
            return await _context.OrdersTables
                .Where(o => o.OrderStatus == status || o.OrderStatus == OrderStatus.ReturnedByCoordinator )
                .OrderByDescending(o => o.CreatedAt)
                .ThenByDescending(o => o.Id)
                .ToListAsync();
        }

        public Task<OrdersTable> GetOrderByNumberAsync(string orderNumber)
        {
            // Get order by its number
            return _context.OrdersTables
                .FirstOrDefaultAsync(o => o.EmployeeNumber == orderNumber);
        }

        public Task<List<OrdersTable>> GetOrdersByStatusesAsync(string[] statuses)
        {
            // Get orders by multiple statuses
            return _context.OrdersTables
                .Where(o => statuses.Contains(o.OrderStatus.ToString()))
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }
    }
} 